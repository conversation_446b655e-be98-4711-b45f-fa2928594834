%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 32f4e526f6c4d2a49979457203108d7a, type: 3}
  m_Name: Ability_200010002
  m_EditorClassIdentifier: 
  blackboard:
  - key: speed
    value: 0
  - key: rangeX
    value: 0
  - key: lifetime
    value: 0
  - key: stun
    value: 0
  - key: energyRecovery
    value: 0
  - key: test
    value: 0
  graphJsons:
  - '{"_type":"Physalia.Flexi.AbilityGraph","nodes":[{"_id":775246,"_position":{"x":-44.0,"y":211.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.OnCastNode"},{"_id":145346,"_position":{"x":350.0,"y":213.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.AppendBoostNode"},{"_id":611588,"_position":{"x":157.0,"y":508.0},"_type":"Physalia.Flexi.BlackboardNode","key":"duration"},{"_id":60027,"_position":{"x":140.0,"y":410.0},"_type":"Physalia.Flexi.BlackboardNode","key":"boostAccel"},{"_id":478235,"_position":{"x":136.0,"y":312.0},"_type":"Physalia.Flexi.BlackboardNode","key":"boostSpeed"},{"_id":465553,"_position":{"x":128.0,"y":110.0},"_type":"Physalia.Flexi.IfElseNode"}],"edges":[{"id1":775246,"port1":"next","id2":465553,"port2":"previousPort"},{"id1":775246,"port1":"caster","id2":145346,"port2":"targets"},{"id1":465553,"port1":"truePort","id2":145346,"port2":"previous"},{"id1":478235,"port1":"value","id2":145346,"port2":"initialSpeed"},{"id1":478235,"port1":"value","id2":145346,"port2":"speedMaxModifier"},{"id1":60027,"port1":"value","id2":145346,"port2":"accelerationModifier"},{"id1":611588,"port1":"value","id2":145346,"port2":"duration"}]}'
  graphGroups:
  - graphs:
    - '{"_type":"Physalia.Flexi.AbilityGraph","nodes":[{"_id":770219,"_position":{"x":1048.66992,"y":81.3875},"_type":"Fanimax.CP.Gameplay.AbilityServices.AppendAbilityNode","gameplayAbilityId":-1,"groupIndex":1,"ignoreClearWhenStunned":false,"showDurationHUD":false},{"_id":45164,"_position":{"x":563.0,"y":277.0},"_type":"Physalia.Flexi.BlackboardNode","key":"90"},{"_id":15975,"_position":{"x":1285.66992,"y":203.3875},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":63208,"_position":{"x":1398.66992,"y":81.3875},"_type":"Fanimax.CP.Gameplay.AbilityServices.AccelerateToMaxNode"},{"_id":833722,"_position":{"x":2252.67,"y":81.3875},"_type":"Fanimax.CP.Gameplay.AbilityServices.FinishNode"},{"_id":664075,"_position":{"x":1720.66992,"y":81.3875},"_type":"Fanimax.CP.Gameplay.AbilityServices.PlayVfxNode","vfxName":"Vfx_004_Lonzo_00_CyberItem_UpperCut_Start","boneType":1,"customBoneId":0,"offsetX":1.75,"offsetY":3.125,"isInWorld":false},{"_id":275682,"_position":{"x":1502.66992,"y":203.3875},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":228145,"_position":{"x":917.67,"y":-6.612503},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":417498,"_position":{"x":650.67,"y":81.3875},"_type":"Fanimax.CP.Gameplay.AbilityServices.OnActivated"},{"_id":689082,"_position":{"x":2009.66992,"y":81.3875},"_type":"Fanimax.CP.Gameplay.AbilityServices.CueCVNode","abilityCVId":-2},{"_id":753509,"_position":{"x":834.0,"y":429.0},"_type":"Physalia.Flexi.IntAddNode","b":1}],"edges":[{"id1":770219,"port1":"next","id2":63208,"port2":"previous"},{"id1":417498,"port1":"next","id2":770219,"port2":"previous"},{"id1":228145,"port1":"character","id2":770219,"port2":"targets"},{"id1":753509,"port1":"result","id2":770219,"port2":"ticks"},{"id1":63208,"port1":"next","id2":664075,"port2":"previous"},{"id1":15975,"port1":"character","id2":63208,"port2":"targets"},{"id1":664075,"port1":"next","id2":689082,"port2":"previous"},{"id1":275682,"port1":"character","id2":664075,"port2":"targets"},{"id1":689082,"port1":"next","id2":833722,"port2":"previous"},{"id1":45164,"port1":"value","id2":753509,"port2":"a"}]}'
  - graphs:
    - "{\"_type\":\"Physalia.Flexi.AbilityGraph\",\"nodes\":[{\"_id\":377778,\"_position\":{\"x\":1144.24731,\"y\":1010.85547},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"lifetime\"},{\"_id\":584857,\"_position\":{\"x\":1399.0,\"y\":447.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.CreateAreaNode\",\"deactivationTime\":15,\"canBreakProjectile\":true,\"gameplayAbilityId\":-1,\"groupIndex\":3,\"vfxDataId\":30071001,\"hitFxDataId\":3000100,\"lockX\":false,\"lockY\":false,\"canHitLevelTrap\":false,\"canBreakLevelTrap\":false,\"ignoreHitIfPreviousHit\":false,\"shapeType\":1,\"pathType\":9,\"rootType\":0,\"pathArg1\":0,\"pathArg2\":0,\"pathArg3\":0},{\"_id\":248604,\"_position\":{\"x\":1145.24731,\"y\":718.855469},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"rangeY\"},{\"_id\":212962,\"_position\":{\"x\":1145.24731,\"y\":620.855469},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"rangeX\"},{\"_id\":676238,\"_position\":{\"x\":1145.24731,\"y\":535.855469},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SelfNode\"},{\"_id\":260465,\"_position\":{\"x\":862.0,\"y\":452.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.DelayNode\",\"nextActionTickSlot\":1,\"countSlot\":2},{\"_id\":862901,\"_position\":{\"x\":767.2473,\"y\":718.855469},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"90\"},{\"_id\":870235,\"_position\":{\"x\":504.247284,\"y\":434.855469},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.OnTickNode\"},{\"_id\":253729,\"_position\":{\"x\":1144.24731,\"y\":1108.85547},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"stun\"},{\"_id\":433,\"_position\":{\"x\":1144.24731,\"y\":1206.85547},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"energyRecovery\"},{\"_id\":239630,\"_position\":{\"x\":1619.0,\"y\":-121.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.PlayVfxNode\",\"vfxName\":\"Vfx_005_Kaiso_00_Passive_Debuff\",\"boneType\":0,\"customBoneId\":0,\"offsetX\":0.0,\"offsetY\":0.0,\"isInWorld\":false},{\"_id\":332432,\"_position\":{\"x\":1150.0,\"y\":-135.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SelfNode\"},{\"_id\":56667,\"_position\":{\"x\":517.0,\"y\":247.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.OnTickNode\"},{\"_id\":751853,\"_position\":{\"x\":1730.0,\"y\":444.0},\"_type\":\"Physalia.Flexi.LogNode\",\"text\":\"\u65B9\u7A0B\u5F0F\"}],\"edges\":[{\"id1\":377778,\"port1\":\"value\",\"id2\":584857,\"port2\":\"lifetime\"},{\"id1\":584857,\"port1\":\"next\",\"id2\":751853,\"port2\":\"previous\"},{\"id1\":260465,\"port1\":\"next\",\"id2\":584857,\"port2\":\"previous\"},{\"id1\":676238,\"port1\":\"character\",\"id2\":584857,\"port2\":\"caster\"},{\"id1\":676238,\"port1\":\"character\",\"id2\":584857,\"port2\":\"anchor\"},{\"id1\":212962,\"port1\":\"value\",\"id2\":584857,\"port2\":\"size\"},{\"id1\":248604,\"port1\":\"value\",\"id2\":584857,\"port2\":\"size2\"},{\"id1\":253729,\"port1\":\"value\",\"id2\":584857,\"port2\":\"stunTicks\"},{\"id1\":433,\"port1\":\"value\",\"id2\":584857,\"port2\":\"energyGainForHit\"},{\"id1\":870235,\"port1\":\"next\",\"id2\":260465,\"port2\":\"previous\"},{\"id1\":862901,\"port1\":\"value\",\"id2\":260465,\"port2\":\"ticks\"},{\"id1\":56667,\"port1\":\"next\",\"id2\":239630,\"port2\":\"previous\"},{\"id1\":332432,\"port1\":\"character\",\"id2\":239630,\"port2\":\"targets\"}]}"
