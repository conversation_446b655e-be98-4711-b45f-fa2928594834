fileFormatVersion: 2
guid: 313c31a5e71f67742b0613812e2b815c
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 0
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Beach
      rect:
        serializedVersion: 2
        x: 1
        y: 1312
        width: 514
        height: 735
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7b6ffe55e27985d40846b915f7a7e4a6
      internalID: 1836296402
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Cloud_1
      rect:
        serializedVersion: 2
        x: 1
        y: 86
        width: 221
        height: 487
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0fd93aeb1e3c0df44a9f8bf1f2fc2223
      internalID: -981505334
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Cloud_2
      rect:
        serializedVersion: 2
        x: 740
        y: 295
        width: 397
        height: 279
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 108f5b96ea738ec4eac152371372adee
      internalID: -410902940
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Cloud_3
      rect:
        serializedVersion: 2
        x: 740
        y: 78
        width: 149
        height: 215
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1dc8e68cb231ece489a73fc917b48937
      internalID: 1709801887
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: CoconutTree_1
      rect:
        serializedVersion: 2
        x: 1101
        y: 93
        width: 88
        height: 200
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d66f53af9c2c14d4ca35774dea5789ea
      internalID: 617093586
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: CoconutTree_1_Leaves
      rect:
        serializedVersion: 2
        x: 252
        y: 4
        width: 127
        height: 136
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1f995ac0efe43e74fa52f37f17a0ada6
      internalID: 641503193
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: CoconutTree_2
      rect:
        serializedVersion: 2
        x: 162
        y: 4
        width: 88
        height: 80
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fa66da5e0cb483b4086e644e46b17d33
      internalID: 560972159
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: CoconutTree_3
      rect:
        serializedVersion: 2
        x: 381
        y: 12
        width: 149
        height: 128
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 591be4ebb3302d1439eb37c802dbce50
      internalID: -448999075
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Contrail_1
      rect:
        serializedVersion: 2
        x: 1
        y: 4
        width: 159
        height: 80
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 71283627af518264f8150a19c78510c1
      internalID: -1516632170
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Contrail_2
      rect:
        serializedVersion: 2
        x: 532
        y: 76
        width: 93
        height: 64
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fa49d0cf47fa5eb41a462926cf66d0cf
      internalID: -743425922
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Front
      rect:
        serializedVersion: 2
        x: 1
        y: 575
        width: 514
        height: 735
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3389a94dcec8cdd4cbd866065ae6174f
      internalID: 1810792066
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Light
      rect:
        serializedVersion: 2
        x: 517
        y: 1312
        width: 514
        height: 735
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 83e48707ce8f9874faea9d6d964461dc
      internalID: -2082052250
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Parasol
      rect:
        serializedVersion: 2
        x: 517
        y: 576
        width: 428
        height: 734
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f3cbc0b38fab2004e9488ebf2ef32cd8
      internalID: -1723933441
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: SunLight
      rect:
        serializedVersion: 2
        x: 947
        y: 691
        width: 514
        height: 619
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2ce7f655c4458664780d013ff90973ec
      internalID: -186743718
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Sky_Light
      rect:
        serializedVersion: 2
        x: 224
        y: 142
        width: 514
        height: 431
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9d6e19e6c327aab469dc880389dc1b9e
      internalID: 739274959
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Crab
      rect:
        serializedVersion: 2
        x: 891
        y: 86
        width: 208
        height: 207
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5bb7b366fb5245042b501e53858b9a12
      internalID: 550117484
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 2ac172b558daa0e4682154883cdb0fd2
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      Beach: 1836296402
      Cloud_1: -981505334
      Cloud_2: -410902940
      Cloud_3: 1709801887
      CoconutTree_1: 617093586
      CoconutTree_1_Leaves: 641503193
      CoconutTree_2: 560972159
      CoconutTree_3: -448999075
      Contrail_1: -1516632170
      Contrail_2: -743425922
      Crab: 550117484
      Front: 1810792066
      Light: -2082052250
      Parasol: -1723933441
      Sky_Light: 739274959
      SunLight: -186743718
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
