%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 32f4e526f6c4d2a49979457203108d7a, type: 3}
  m_Name: Ability_3001200
  m_EditorClassIdentifier: 
  blackboard:
  - key: lifetime
    value: 0
  - key: rangeX
    value: 0
  - key: rangeY
    value: 0
  - key: invulnerable
    value: 0
  - key: stun
    value: 0
  - key: energyRecovery
    value: 0
  - key: cooldown
    value: 0
  - key: offsetX
    value: 0
  - key: delay
    value: 0
  - key: rangeX0
    value: 0
  graphJsons: []
  graphGroups:
  - graphs:
    - "{\"_type\":\"Physalia.Flexi.AbilityGraph\",\"nodes\":[{\"_id\":448877,\"_position\":{\"x\":-555.0,\"y\":715.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.OnActivated\"},{\"_id\":782853,\"_position\":{\"x\":7307.0,\"y\":803.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.FinishNode\"},{\"_id\":248742,\"_position\":{\"x\":-554.0,\"y\":1040.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.OnActivating\"},{\"_id\":954640,\"_position\":{\"x\":-304.0,\"y\":1040.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.AppendAbilityNode\",\"gameplayAbilityId\":-1,\"groupIndex\":1,\"ignoreClearWhenStunned\":false,\"showDurationHUD\":false},{\"_id\":394041,\"_position\":{\"x\":5926.0,\"y\":803.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.RemoveAbilityNode\",\"gameplayAbilityId\":-1,\"groupIndex\":1},{\"_id\":209187,\"_position\":{\"x\":5801.0,\"y\":682.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SelfNode\"},{\"_id\":238501,\"_position\":{\"x\":-551.0,\"y\":535.0},\"_type\":\"Physalia.Flexi.StringNode\",\"text\":\"rangeX=5,
      \u539F\u578B\u7D50\u679CDistance0~5.5\u90FD\u6703\u4E2D\"},{\"_id\":296641,\"_position\":{\"x\":4550.0,\"y\":563.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SelfNode\"},{\"_id\":728522,\"_position\":{\"x\":4549.0,\"y\":1018.0},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"stun\"},{\"_id\":307993,\"_position\":{\"x\":4496.0,\"y\":1116.0},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"energyRecovery\"},{\"_id\":596396,\"_position\":{\"x\":6324.0,\"y\":803.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.AddCameraBumpByActorNode\",\"ownerOnly\":true,\"bumpId\":3001100},{\"_id\":364763,\"_position\":{\"x\":6172.0,\"y\":983.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SelfNode\"},{\"_id\":215785,\"_position\":{\"x\":5192.0,\"y\":668.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SelfNode\"},{\"_id\":559972,\"_position\":{\"x\":5337.0,\"y\":803.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.PlayVfxNode\",\"vfxName\":\"Vfx_001_Park_00_CpAbility_Trail\",\"boneType\":0,\"customBoneId\":0,\"offsetX\":0.0,\"offsetY\":0.0,\"isInWorld\":false},{\"_id\":704526,\"_position\":{\"x\":4821.0,\"y\":531.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.CreateAreaNode\",\"canBreakProjectile\":true,\"gameplayAbilityId\":-1,\"groupIndex\":3,\"lockX\":false,\"lockY\":false,\"canHitLevelTrap\":false,\"canBreakLevelTrap\":false,\"ignoreHitIfPreviousHit\":false,\"shapeType\":2,\"pathType\":0,\"rootType\":1,\"pathArg1\":0,\"pathArg2\":0,\"pathArg3\":0},{\"_id\":28981,\"_position\":{\"x\":4550.0,\"y\":822.0},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"rangeY\"},{\"_id\":424120,\"_position\":{\"x\":4549.0,\"y\":920.0},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"lifetime\"},{\"_id\":550377,\"_position\":{\"x\":5575.0,\"y\":803.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.CueCVNode\",\"abilityCVId\":3001100},{\"_id\":195781,\"_position\":{\"x\":364.0,\"y\":715.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.AppendAbilityNode\",\"gameplayAbilityId\":-1,\"groupIndex\":2,\"overwrite\":true,\"ignoreClearWhenStunned\":false,\"showDurationHUD\":false},{\"_id\":38838,\"_position\":{\"x\":217.0,\"y\":625.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SelfNode\"},{\"_id\":740545,\"_position\":{\"x\":188.0,\"y\":999.0},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"invulnerable\"},{\"_id\":326637,\"_position\":{\"x\":815.0,\"y\":715.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.TeleportNode\"},{\"_id\":740105,\"_position\":{\"x\":599.0,\"y\":901.0},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"rangeX\"},{\"_id\":587372,\"_position\":{\"x\":620.0,\"y\":625.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SelfNode\"},{\"_id\":988056,\"_position\":{\"x\":1258.0,\"y\":737.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetMidpointNode\"},{\"_id\":451529,\"_position\":{\"x\":3817.0,\"y\":121.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.PlayVfxInWorldPositionNode\",\"vfxName\":\"Vfx_001_Park_00_CpAbility_CharHit\"},{\"_id\":292260,\"_position\":{\"x\":1258.0,\"y\":851.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetVectorNode\"},{\"_id\":216539,\"_position\":{\"x\":1427.0,\"y\":851.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetDirectionInDegreeNode\"},{\"_id\":638116,\"_position\":{\"x\":1705.0,\"y\":851.0},\"_type\":\"Physalia.Flexi.FloatDivideNode\",\"b\":10000.0},{\"_id\":615994,\"_position\":{\"x\":1427.0,\"y\":984.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetMagnitudeNode\"},{\"_id\":890686,\"_position\":{\"x\":3066.0,\"y\":166.0},\"_type\":\"Physalia.Flexi.IfElseNode\"},{\"_id\":819546,\"_position\":{\"x\":2010.0,\"y\":903.0},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"rangeX\"},{\"_id\":407106,\"_position\":{\"x\":2183.0,\"y\":838.0},\"_type\":\"Physalia.Flexi.FloatDivideNode\"},{\"_id\":117754,\"_position\":{\"x\":4191.0,\"y\":467.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.PlayVfxInWorldPositionWithScaleNode\",\"vfxName\":\"Vfx_001_Park_00_CpAbility\",\"sfxName\":\"Sfx_001_Park_00_CpAbility\",\"scaleY\":1.0},{\"_id\":637916,\"_position\":{\"x\":1705.0,\"y\":984.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.ClampNode\",\"min\":5000},{\"_id\":686009,\"_position\":{\"x\":1478.0,\"y\":1120.0},\"_type\":\"Physalia.Flexi.IntSubtractNode\"},{\"_id\":935746,\"_position\":{\"x\":1193.0,\"y\":1249.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetVector2XYNode\"},{\"_id\":412247,\"_position\":{\"x\":1193.0,\"y\":1120.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetVector2XYNode\"},{\"_id\":222448,\"_position\":{\"x\":1478.0,\"y\":1249.0},\"_type\":\"Physalia.Flexi.IntSubtractNode\"},{\"_id\":644379,\"_position\":{\"x\":3385.0,\"y\":-28.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.AddCameraShakeByActorNode\",\"shakeId\":30011001},{\"_id\":510086,\"_position\":{\"x\":3385.0,\"y\":121.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.AddCameraShakeByActorNode\",\"shakeId\":30011002},{\"_id\":661463,\"_position\":{\"x\":3247.0,\"y\":-14.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SelfNode\"},{\"_id\":224309,\"_position\":{\"x\":3247.0,\"y\":255.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SelfNode\"},{\"_id\":570515,\"_position\":{\"x\":2404.0,\"y\":189.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetCharactersInRectangleNode\",\"excludeAlly\":true},{\"_id\":691905,\"_position\":{\"x\":2671.0,\"y\":189.0},\"_type\":\"Physalia.Flexi.CountNode\"},{\"_id\":70093,\"_position\":{\"x\":2888.0,\"y\":189.0},\"_type\":\"Physalia.Flexi.GreaterNode\"},{\"_id\":438419,\"_position\":{\"x\":2245.0,\"y\":189.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SelfNode\"},{\"_id\":897766,\"_position\":{\"x\":2223.0,\"y\":275.0},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"rangeY\"},{\"_id\":994547,\"_position\":{\"x\":1374.0,\"y\":285.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetCharacterCenterNode\"},{\"_id\":630274,\"_position\":{\"x\":1593.0,\"y\":486.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetVector2XYNode\"},{\"_id\":764999,\"_position\":{\"x\":1593.0,\"y\":285.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetVector2XYNode\"},{\"_id\":239102,\"_position\":{\"x\":1929.0,\"y\":486.0},\"_type\":\"Physalia.Flexi.IntSubtractNode\"},{\"_id\":963243,\"_position\":{\"x\":1929.0,\"y\":285.0},\"_type\":\"Physalia.Flexi.IntSubtractNode\"},{\"_id\":869753,\"_position\":{\"x\":1234.0,\"y\":285.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SelfNode\"},{\"_id\":384562,\"_position\":{\"x\":-1275.0,\"y\":643.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetVectorNode\"},{\"_id\":728640,\"_position\":{\"x\":-912.0,\"y\":638.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetMidpointNode\"},{\"_id\":894970,\"_position\":{\"x\":-1273.0,\"y\":859.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetVector2XYNode\"},{\"_id\":689763,\"_position\":{\"x\":-915.0,\"y\":865.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetDirectionInDegreeNode\"},{\"_id\":59268,\"_position\":{\"x\":-919.0,\"y\":1078.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetMagnitudeNode\"},{\"_id\":708498,\"_position\":{\"x\":-1276.0,\"y\":536.0},\"_type\":\"Physalia.Flexi.StringNode\",\"text\":\"\u53D6\u5F97\u5169\u9EDE\u9593\u7684\u5411\u91CF\"},{\"_id\":928484,\"_position\":{\"x\":-1275.0,\"y\":753.0},\"_type\":\"Physalia.Flexi.StringNode\",\"text\":\"\u53D6\u5F97\u8A72\u4F4D\u7F6E/\u5411\u91CF\u7684(x,y)\"},{\"_id\":570123,\"_position\":{\"x\":-915.0,\"y\":539.0},\"_type\":\"Physalia.Flexi.StringNode\",\"text\":\"\u53D6\u5F97\u5169\u9EDE\u9593\u7684\u4E2D\u9593\u9EDE\"},{\"_id\":418484,\"_position\":{\"x\":-914.0,\"y\":762.0},\"_type\":\"Physalia.Flexi.StringNode\",\"text\":\"\u53D6\u5F97\u5169\u9EDE\u9593\u7684\u65B9\u5411(\u914D\u5408\u5411\u91CF\u4F7F\u7528)\"},{\"_id\":860107,\"_position\":{\"x\":-915.0,\"y\":965.0},\"_type\":\"Physalia.Flexi.StringNode\",\"text\":\"\u53D6\u5F97\u5169\u9EDE\u9593\u7684\u8DDD\u96E2\"},{\"_id\":621684,\"_position\":{\"x\":-1272.0,\"y\":1086.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetCharactersInRectangleNode\",\"excludeAlly\":true},{\"_id\":703625,\"_position\":{\"x\":-1274.0,\"y\":974.0},\"_type\":\"Physalia.Flexi.StringNode\",\"text\":\"\u8A08\u7B97\u7BC4\u570D\u5167\u7684\u89D2\u8272\"},{\"_id\":211602,\"_position\":{\"x\":6827.0,\"y\":713.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SelfNode\"},{\"_id\":820407,\"_position\":{\"x\":6974.0,\"y\":803.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.AppendAbilityNode\",\"gameplayAbilityId\":-1,\"groupIndex\":4,\"ticks\":-1,\"overwrite\":true,\"ignoreClearWhenStunned\":false,\"showDurationHUD\":false},{\"_id\":994730,\"_position\":{\"x\":6988.0,\"y\":665.0},\"_type\":\"Physalia.Flexi.StringNode\",\"text\":\"\u7D66\u4E88\u56DE\u65AC\"}],\"edges\":[{\"id1\":448877,\"port1\":\"next\",\"id2\":195781,\"port2\":\"previous\"},{\"id1\":195781,\"port1\":\"next\",\"id2\":326637,\"port2\":\"previous\"},{\"id1\":38838,\"port1\":\"character\",\"id2\":195781,\"port2\":\"targets\"},{\"id1\":740545,\"port1\":\"value\",\"id2\":195781,\"port2\":\"ticks\"},{\"id1\":326637,\"port1\":\"next\",\"id2\":890686,\"port2\":\"previousPort\"},{\"id1\":326637,\"port1\":\"originCenter\",\"id2\":988056,\"port2\":\"a\"},{\"id1\":326637,\"port1\":\"originCenter\",\"id2\":292260,\"port2\":\"a\"},{\"id1\":326637,\"port1\":\"originCenter\",\"id2\":412247,\"port2\":\"value\"},{\"id1\":326637,\"port1\":\"resultCenter\",\"id2\":988056,\"port2\":\"b\"},{\"id1\":326637,\"port1\":\"resultCenter\",\"id2\":292260,\"port2\":\"b\"},{\"id1\":326637,\"port1\":\"resultCenter\",\"id2\":935746,\"port2\":\"value\"},{\"id1\":587372,\"port1\":\"character\",\"id2\":326637,\"port2\":\"target\"},{\"id1\":740105,\"port1\":\"value\",\"id2\":326637,\"port2\":\"offsetX\"},{\"id1\":890686,\"port1\":\"truePort\",\"id2\":644379,\"port2\":\"previous\"},{\"id1\":890686,\"port1\":\"falsePort\",\"id2\":510086,\"port2\":\"previous\"},{\"id1\":70093,\"port1\":\"result\",\"id2\":890686,\"port2\":\"conditionPort\"},{\"id1\":644379,\"port1\":\"next\",\"id2\":451529,\"port2\":\"previous\"},{\"id1\":661463,\"port1\":\"character\",\"id2\":644379,\"port2\":\"targets\"},{\"id1\":451529,\"port1\":\"next\",\"id2\":117754,\"port2\":\"previous\"},{\"id1\":988056,\"port1\":\"result\",\"id2\":451529,\"port2\":\"source\"},{\"id1\":638116,\"port1\":\"result\",\"id2\":451529,\"port2\":\"degree\"},{\"id1\":117754,\"port1\":\"next\",\"id2\":704526,\"port2\":\"previous\"},{\"id1\":510086,\"port1\":\"next\",\"id2\":117754,\"port2\":\"previous\"},{\"id1\":988056,\"port1\":\"result\",\"id2\":117754,\"port2\":\"source\"},{\"id1\":638116,\"port1\":\"result\",\"id2\":117754,\"port2\":\"degree\"},{\"id1\":407106,\"port1\":\"result\",\"id2\":117754,\"port2\":\"scaleX\"},{\"id1\":704526,\"port1\":\"next\",\"id2\":559972,\"port2\":\"previous\"},{\"id1\":296641,\"port1\":\"character\",\"id2\":704526,\"port2\":\"caster\"},{\"id1\":296641,\"port1\":\"character\",\"id2\":704526,\"port2\":\"anchor\"},{\"id1\":637916,\"port1\":\"result\",\"id2\":704526,\"port2\":\"size\"},{\"id1\":28981,\"port1\":\"value\",\"id2\":704526,\"port2\":\"size2\"},{\"id1\":216539,\"port1\":\"directionInDegree\",\"id2\":704526,\"port2\":\"rotation\"},{\"id1\":686009,\"port1\":\"result\",\"id2\":704526,\"port2\":\"offsetX\"},{\"id1\":222448,\"port1\":\"result\",\"id2\":704526,\"port2\":\"offsetY\"},{\"id1\":424120,\"port1\":\"value\",\"id2\":704526,\"port2\":\"lifetime\"},{\"id1\":728522,\"port1\":\"value\",\"id2\":704526,\"port2\":\"stunTicks\"},{\"id1\":307993,\"port1\":\"value\",\"id2\":704526,\"port2\":\"energyGainForHit\"},{\"id1\":559972,\"port1\":\"next\",\"id2\":550377,\"port2\":\"previous\"},{\"id1\":215785,\"port1\":\"character\",\"id2\":559972,\"port2\":\"targets\"},{\"id1\":550377,\"port1\":\"next\",\"id2\":394041,\"port2\":\"previous\"},{\"id1\":394041,\"port1\":\"next\",\"id2\":596396,\"port2\":\"previous\"},{\"id1\":209187,\"port1\":\"character\",\"id2\":394041,\"port2\":\"targets\"},{\"id1\":596396,\"port1\":\"next\",\"id2\":820407,\"port2\":\"previous\"},{\"id1\":364763,\"port1\":\"character\",\"id2\":596396,\"port2\":\"targets\"},{\"id1\":820407,\"port1\":\"next\",\"id2\":782853,\"port2\":\"previous\"},{\"id1\":211602,\"port1\":\"character\",\"id2\":820407,\"port2\":\"targets\"},{\"id1\":637916,\"port1\":\"result\",\"id2\":407106,\"port2\":\"a\"},{\"id1\":637916,\"port1\":\"result\",\"id2\":570515,\"port2\":\"width\"},{\"id1\":615994,\"port1\":\"magnitude\",\"id2\":637916,\"port2\":\"value\"},{\"id1\":615994,\"port1\":\"magnitude\",\"id2\":637916,\"port2\":\"max\"},{\"id1\":819546,\"port1\":\"value\",\"id2\":407106,\"port2\":\"b\"},{\"id1\":570515,\"port1\":\"characters\",\"id2\":691905,\"port2\":\"collectionPort\"},{\"id1\":438419,\"port1\":\"character\",\"id2\":570515,\"port2\":\"source\"},{\"id1\":897766,\"port1\":\"value\",\"id2\":570515,\"port2\":\"height\"},{\"id1\":963243,\"port1\":\"result\",\"id2\":570515,\"port2\":\"offsetX\"},{\"id1\":239102,\"port1\":\"result\",\"id2\":570515,\"port2\":\"offsetY\"},{\"id1\":216539,\"port1\":\"directionInDegree\",\"id2\":570515,\"port2\":\"angle\"},{\"id1\":691905,\"port1\":\"countPort\",\"id2\":70093,\"port2\":\"a\"},{\"id1\":630274,\"port1\":\"x\",\"id2\":963243,\"port2\":\"a\"},{\"id1\":764999,\"port1\":\"x\",\"id2\":963243,\"port2\":\"b\"},{\"id1\":630274,\"port1\":\"y\",\"id2\":239102,\"port2\":\"a\"},{\"id1\":988056,\"port1\":\"result\",\"id2\":630274,\"port2\":\"value\"},{\"id1\":764999,\"port1\":\"y\",\"id2\":239102,\"port2\":\"b\"},{\"id1\":994547,\"port1\":\"center\",\"id2\":764999,\"port2\":\"value\"},{\"id1\":869753,\"port1\":\"character\",\"id2\":994547,\"port2\":\"target\"},{\"id1\":216539,\"port1\":\"directionInDegree\",\"id2\":638116,\"port2\":\"a\"},{\"id1\":292260,\"port1\":\"result\",\"id2\":216539,\"port2\":\"vector\"},{\"id1\":292260,\"port1\":\"result\",\"id2\":615994,\"port2\":\"vector\"},{\"id1\":412247,\"port1\":\"x\",\"id2\":686009,\"port2\":\"a\"},{\"id1\":935746,\"port1\":\"x\",\"id2\":686009,\"port2\":\"b\"},{\"id1\":412247,\"port1\":\"y\",\"id2\":222448,\"port2\":\"a\"},{\"id1\":935746,\"port1\":\"y\",\"id2\":222448,\"port2\":\"b\"},{\"id1\":224309,\"port1\":\"character\",\"id2\":510086,\"port2\":\"targets\"},{\"id1\":248742,\"port1\":\"next\",\"id2\":954640,\"port2\":\"previous\"},{\"id1\":248742,\"port1\":\"self\",\"id2\":954640,\"port2\":\"targets\"}]}"
  - graphs:
    - '{"_type":"Physalia.Flexi.AbilityGraph","nodes":[{"_id":246882,"_position":{"x":123.0,"y":226.0},"_type":"Physalia.Flexi.StatRefreshEventNode","order":0},{"_id":203326,"_position":{"x":459.0,"y":226.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.LockVelocityModifierNode","y":true}],"edges":[{"id1":246882,"port1":"next","id2":203326,"port2":"previous"}]}'
  - graphs:
    - '{"_type":"Physalia.Flexi.AbilityGraph","nodes":[{"_id":974112,"_position":{"x":221.0,"y":209.0},"_type":"Physalia.Flexi.StatRefreshEventNode","order":0},{"_id":779560,"_position":{"x":468.0,"y":209.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.InvulnerableModifierNode"}],"edges":[{"id1":974112,"port1":"next","id2":779560,"port2":"previous"}]}'
  - graphs:
    - '{"_type":"Physalia.Flexi.AbilityGraph","nodes":[{"_id":69427,"_position":{"x":591.0,"y":205.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.SetAbilityCooldownNode","gameplayAbilityId":-1},{"_id":210833,"_position":{"x":402.0,"y":442.0},"_type":"Physalia.Flexi.BlackboardNode","key":"cooldown"},{"_id":162131,"_position":{"x":282.0,"y":205.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.OnProjectileApplyStunNode"},{"_id":645136,"_position":{"x":1169.0,"y":189.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.AbilityLogNode","eventType":2,"param1":1},{"_id":293069,"_position":{"x":282.0,"y":634.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.OnObjectBreakOther"},{"_id":825310,"_position":{"x":654.0,"y":634.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.AbilityLogNode","eventType":11,"param2":1},{"_id":921370,"_position":{"x":368.0,"y":80.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.GetItemAbilityAtSlotNode"},{"_id":663024,"_position":{"x":135.0,"y":78.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":61813,"_position":{"x":932.0,"y":107.0},"_type":"Physalia.Flexi.LogNode"}],"edges":[{"id1":69427,"port1":"next","id2":61813,"port2":"previous"},{"id1":162131,"port1":"next","id2":69427,"port2":"previous"},{"id1":162131,"port1":"instigator","id2":69427,"port2":"target"},{"id1":921370,"port1":"gameplayAbilityId","id2":69427,"port2":"gameplayAbilityId"},{"id1":210833,"port1":"value","id2":69427,"port2":"cooldownTicks"},{"id1":61813,"port1":"next","id2":645136,"port2":"previous"},{"id1":921370,"port1":"gameplayAbilityId","id2":61813,"port2":"text"},{"id1":162131,"port1":"instigator","id2":645136,"port2":"instigator"},{"id1":162131,"port1":"target","id2":645136,"port2":"targets"},{"id1":162131,"port1":"instigator","id2":921370,"port2":"target"},{"id1":293069,"port1":"next","id2":825310,"port2":"previous"},{"id1":293069,"port1":"caster","id2":825310,"port2":"instigator"}]}'
  - graphs:
    - "{\"_type\":\"Physalia.Flexi.AbilityGraph\",\"nodes\":[{\"_id\":10404,\"_position\":{\"x\":-565.0,\"y\":558.0},\"_type\":\"Physalia.Flexi.StringNode\",\"text\":\"\u56DE\u65AC\"},{\"_id\":432049,\"_position\":{\"x\":6066.0,\"y\":822.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.AddCameraBumpByActorNode\",\"ownerOnly\":true,\"bumpId\":3001100},{\"_id\":139555,\"_position\":{\"x\":5945.0,\"y\":1004.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SelfNode\"},{\"_id\":214743,\"_position\":{\"x\":5699.0,\"y\":824.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.RemoveAbilityNode\",\"gameplayAbilityId\":-1,\"groupIndex\":1},{\"_id\":753455,\"_position\":{\"x\":5574.0,\"y\":703.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SelfNode\"},{\"_id\":211818,\"_position\":{\"x\":4965.0,\"y\":689.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SelfNode\"},{\"_id\":433528,\"_position\":{\"x\":5110.0,\"y\":824.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.PlayVfxNode\",\"vfxName\":\"Vfx_001_Park_00_CpAbility_Trail\",\"boneType\":0,\"customBoneId\":0,\"offsetX\":0.0,\"offsetY\":0.0,\"isInWorld\":false},{\"_id\":688042,\"_position\":{\"x\":4594.0,\"y\":552.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.CreateAreaNode\",\"canBreakProjectile\":true,\"gameplayAbilityId\":-1,\"groupIndex\":3,\"lockX\":false,\"lockY\":false,\"canHitLevelTrap\":false,\"canBreakLevelTrap\":false,\"ignoreHitIfPreviousHit\":false,\"shapeType\":2,\"pathType\":0,\"rootType\":1,\"pathArg1\":0,\"pathArg2\":0,\"pathArg3\":0},{\"_id\":880502,\"_position\":{\"x\":5348.0,\"y\":824.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.CueCVNode\",\"abilityCVId\":3001100},{\"_id\":680401,\"_position\":{\"x\":-10.0,\"y\":646.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SelfNode\"},{\"_id\":229372,\"_position\":{\"x\":-39.0,\"y\":1020.0},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"invulnerable\"},{\"_id\":861585,\"_position\":{\"x\":4323.0,\"y\":584.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SelfNode\"},{\"_id\":947673,\"_position\":{\"x\":4322.0,\"y\":1039.0},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"stun\"},{\"_id\":114151,\"_position\":{\"x\":4269.0,\"y\":1137.0},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"energyRecovery\"},{\"_id\":142363,\"_position\":{\"x\":4323.0,\"y\":843.0},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"rangeY\"},{\"_id\":303200,\"_position\":{\"x\":4322.0,\"y\":941.0},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"lifetime\"},{\"_id\":684069,\"_position\":{\"x\":137.0,\"y\":736.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.AppendAbilityNode\",\"gameplayAbilityId\":-1,\"groupIndex\":2,\"overwrite\":true,\"ignoreClearWhenStunned\":false,\"showDurationHUD\":false},{\"_id\":468498,\"_position\":{\"x\":588.0,\"y\":736.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.TeleportNode\"},{\"_id\":103789,\"_position\":{\"x\":372.0,\"y\":922.0},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"rangeX0\"},{\"_id\":881662,\"_position\":{\"x\":393.0,\"y\":646.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SelfNode\"},{\"_id\":433755,\"_position\":{\"x\":1031.0,\"y\":758.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetMidpointNode\"},{\"_id\":223808,\"_position\":{\"x\":3590.0,\"y\":142.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.PlayVfxInWorldPositionNode\",\"vfxName\":\"Vfx_001_Park_00_CpAbility_CharHit\"},{\"_id\":382587,\"_position\":{\"x\":1031.0,\"y\":872.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetVectorNode\"},{\"_id\":125611,\"_position\":{\"x\":1200.0,\"y\":872.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetDirectionInDegreeNode\"},{\"_id\":267142,\"_position\":{\"x\":1478.0,\"y\":872.0},\"_type\":\"Physalia.Flexi.FloatDivideNode\",\"b\":10000.0},{\"_id\":114418,\"_position\":{\"x\":1200.0,\"y\":1005.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetMagnitudeNode\"},{\"_id\":681026,\"_position\":{\"x\":2839.0,\"y\":187.0},\"_type\":\"Physalia.Flexi.IfElseNode\"},{\"_id\":635257,\"_position\":{\"x\":1783.0,\"y\":924.0},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"rangeX0\"},{\"_id\":290804,\"_position\":{\"x\":1956.0,\"y\":859.0},\"_type\":\"Physalia.Flexi.FloatDivideNode\"},{\"_id\":173347,\"_position\":{\"x\":3964.0,\"y\":488.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.PlayVfxInWorldPositionWithScaleNode\",\"vfxName\":\"Vfx_001_Park_00_CpAbility\",\"sfxName\":\"Sfx_001_Park_00_CpAbility\",\"scaleY\":1.0},{\"_id\":765106,\"_position\":{\"x\":1478.0,\"y\":1005.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.ClampNode\",\"min\":5000},{\"_id\":306494,\"_position\":{\"x\":1251.0,\"y\":1141.0},\"_type\":\"Physalia.Flexi.IntSubtractNode\"},{\"_id\":596821,\"_position\":{\"x\":966.0,\"y\":1270.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetVector2XYNode\"},{\"_id\":661649,\"_position\":{\"x\":966.0,\"y\":1141.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetVector2XYNode\"},{\"_id\":458012,\"_position\":{\"x\":1251.0,\"y\":1270.0},\"_type\":\"Physalia.Flexi.IntSubtractNode\"},{\"_id\":858876,\"_position\":{\"x\":3158.0,\"y\":-7.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.AddCameraShakeByActorNode\",\"shakeId\":30011001},{\"_id\":494892,\"_position\":{\"x\":3158.0,\"y\":142.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.AddCameraShakeByActorNode\",\"shakeId\":30011002},{\"_id\":264166,\"_position\":{\"x\":3020.0,\"y\":7.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SelfNode\"},{\"_id\":493074,\"_position\":{\"x\":3020.0,\"y\":276.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SelfNode\"},{\"_id\":617363,\"_position\":{\"x\":2177.0,\"y\":210.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetCharactersInRectangleNode\",\"excludeAlly\":true},{\"_id\":819354,\"_position\":{\"x\":2444.0,\"y\":210.0},\"_type\":\"Physalia.Flexi.CountNode\"},{\"_id\":312039,\"_position\":{\"x\":2661.0,\"y\":210.0},\"_type\":\"Physalia.Flexi.GreaterNode\"},{\"_id\":726775,\"_position\":{\"x\":2018.0,\"y\":210.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SelfNode\"},{\"_id\":834685,\"_position\":{\"x\":1996.0,\"y\":296.0},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"rangeY\"},{\"_id\":237163,\"_position\":{\"x\":1147.0,\"y\":306.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetCharacterCenterNode\"},{\"_id\":598809,\"_position\":{\"x\":1366.0,\"y\":507.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetVector2XYNode\"},{\"_id\":933724,\"_position\":{\"x\":1366.0,\"y\":306.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetVector2XYNode\"},{\"_id\":912583,\"_position\":{\"x\":1702.0,\"y\":507.0},\"_type\":\"Physalia.Flexi.IntSubtractNode\"},{\"_id\":162039,\"_position\":{\"x\":1702.0,\"y\":306.0},\"_type\":\"Physalia.Flexi.IntSubtractNode\"},{\"_id\":941799,\"_position\":{\"x\":1007.0,\"y\":306.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SelfNode\"},{\"_id\":439693,\"_position\":{\"x\":41.63507,\"y\":1454.91541},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.OnActivating\"},{\"_id\":771563,\"_position\":{\"x\":291.635071,\"y\":1454.91541},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.AppendAbilityNode\",\"gameplayAbilityId\":-1,\"groupIndex\":1,\"ignoreClearWhenStunned\":false,\"showDurationHUD\":false},{\"_id\":36262,\"_position\":{\"x\":-847.0,\"y\":734.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.OnTickNode\"},{\"_id\":179257,\"_position\":{\"x\":-209.0,\"y\":734.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.DelayNode\",\"countSlot\":1},{\"_id\":977079,\"_position\":{\"x\":-453.0,\"y\":958.0},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"delay\"},{\"_id\":531595,\"_position\":{\"x\":6463.0,\"y\":818.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.RemoveAbilityNode\",\"gameplayAbilityId\":-1,\"groupIndex\":4},{\"_id\":69725,\"_position\":{\"x\":-489.0,\"y\":734.0},\"_type\":\"Physalia.Flexi.LogNode\"},{\"_id\":858153,\"_position\":{\"x\":227.0,\"y\":2341.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetAbilityByGroupNode\"},{\"_id\":973417,\"_position\":{\"x\":225.0,\"y\":2230.0},\"_type\":\"Physalia.Flexi.StringNode\",\"text\":\"\u6C92\u6709\u7B49\u7D1A\u7684ID(\u627E\u7B2C\u4E00\u500B)\"},{\"_id\":441190,\"_position\":{\"x\":223.0,\"y\":2090.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetAbilityCooldownNode\"},{\"_id\":171083,\"_position\":{\"x\":567.0,\"y\":2172.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.MaxNode\"},{\"_id\":766002,\"_position\":{\"x\":796.0,\"y\":2105.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SetAbilityCooldownNode\",\"ignoreSetCooldownTicks\":true},{\"_id\":904294,\"_position\":{\"x\":433.0,\"y\":1900.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.EnergyCostMultiplierOffsetModifierNode\"},{\"_id\":796870,\"_position\":{\"x\":223.0,\"y\":1993.0},\"_type\":\"Physalia.Flexi.StringNode\",\"text\":\"\u8A2D\u5B9A\u51B7\u537B\"},{\"_id\":287759,\"_position\":{\"x\":430.0,\"y\":1797.0},\"_type\":\"Physalia.Flexi.StringNode\",\"text\":\"\u6E1B\u5C11\u6D88\u8017\"}],\"edges\":[{\"id1\":10404,\"port1\":\"output\",\"id2\":69725,\"port2\":\"text\"},{\"id1\":69725,\"port1\":\"next\",\"id2\":179257,\"port2\":\"previous\"},{\"id1\":36262,\"port1\":\"next\",\"id2\":69725,\"port2\":\"previous\"},{\"id1\":179257,\"port1\":\"next\",\"id2\":684069,\"port2\":\"previous\"},{\"id1\":977079,\"port1\":\"value\",\"id2\":179257,\"port2\":\"ticks\"},{\"id1\":684069,\"port1\":\"next\",\"id2\":468498,\"port2\":\"previous\"},{\"id1\":680401,\"port1\":\"character\",\"id2\":684069,\"port2\":\"targets\"},{\"id1\":229372,\"port1\":\"value\",\"id2\":684069,\"port2\":\"ticks\"},{\"id1\":468498,\"port1\":\"next\",\"id2\":681026,\"port2\":\"previousPort\"},{\"id1\":468498,\"port1\":\"originCenter\",\"id2\":433755,\"port2\":\"a\"},{\"id1\":468498,\"port1\":\"originCenter\",\"id2\":382587,\"port2\":\"a\"},{\"id1\":468498,\"port1\":\"originCenter\",\"id2\":661649,\"port2\":\"value\"},{\"id1\":468498,\"port1\":\"resultCenter\",\"id2\":433755,\"port2\":\"b\"},{\"id1\":468498,\"port1\":\"resultCenter\",\"id2\":382587,\"port2\":\"b\"},{\"id1\":468498,\"port1\":\"resultCenter\",\"id2\":596821,\"port2\":\"value\"},{\"id1\":881662,\"port1\":\"character\",\"id2\":468498,\"port2\":\"target\"},{\"id1\":103789,\"port1\":\"value\",\"id2\":468498,\"port2\":\"offsetX\"},{\"id1\":681026,\"port1\":\"truePort\",\"id2\":858876,\"port2\":\"previous\"},{\"id1\":681026,\"port1\":\"falsePort\",\"id2\":494892,\"port2\":\"previous\"},{\"id1\":312039,\"port1\":\"result\",\"id2\":681026,\"port2\":\"conditionPort\"},{\"id1\":858876,\"port1\":\"next\",\"id2\":223808,\"port2\":\"previous\"},{\"id1\":264166,\"port1\":\"character\",\"id2\":858876,\"port2\":\"targets\"},{\"id1\":223808,\"port1\":\"next\",\"id2\":173347,\"port2\":\"previous\"},{\"id1\":433755,\"port1\":\"result\",\"id2\":223808,\"port2\":\"source\"},{\"id1\":267142,\"port1\":\"result\",\"id2\":223808,\"port2\":\"degree\"},{\"id1\":173347,\"port1\":\"next\",\"id2\":688042,\"port2\":\"previous\"},{\"id1\":494892,\"port1\":\"next\",\"id2\":173347,\"port2\":\"previous\"},{\"id1\":433755,\"port1\":\"result\",\"id2\":173347,\"port2\":\"source\"},{\"id1\":267142,\"port1\":\"result\",\"id2\":173347,\"port2\":\"degree\"},{\"id1\":290804,\"port1\":\"result\",\"id2\":173347,\"port2\":\"scaleX\"},{\"id1\":688042,\"port1\":\"next\",\"id2\":433528,\"port2\":\"previous\"},{\"id1\":861585,\"port1\":\"character\",\"id2\":688042,\"port2\":\"caster\"},{\"id1\":861585,\"port1\":\"character\",\"id2\":688042,\"port2\":\"anchor\"},{\"id1\":765106,\"port1\":\"result\",\"id2\":688042,\"port2\":\"size\"},{\"id1\":142363,\"port1\":\"value\",\"id2\":688042,\"port2\":\"size2\"},{\"id1\":125611,\"port1\":\"directionInDegree\",\"id2\":688042,\"port2\":\"rotation\"},{\"id1\":306494,\"port1\":\"result\",\"id2\":688042,\"port2\":\"offsetX\"},{\"id1\":458012,\"port1\":\"result\",\"id2\":688042,\"port2\":\"offsetY\"},{\"id1\":303200,\"port1\":\"value\",\"id2\":688042,\"port2\":\"lifetime\"},{\"id1\":947673,\"port1\":\"value\",\"id2\":688042,\"port2\":\"stunTicks\"},{\"id1\":114151,\"port1\":\"value\",\"id2\":688042,\"port2\":\"energyGainForHit\"},{\"id1\":433528,\"port1\":\"next\",\"id2\":880502,\"port2\":\"previous\"},{\"id1\":211818,\"port1\":\"character\",\"id2\":433528,\"port2\":\"targets\"},{\"id1\":880502,\"port1\":\"next\",\"id2\":214743,\"port2\":\"previous\"},{\"id1\":214743,\"port1\":\"next\",\"id2\":432049,\"port2\":\"previous\"},{\"id1\":753455,\"port1\":\"character\",\"id2\":214743,\"port2\":\"targets\"},{\"id1\":432049,\"port1\":\"next\",\"id2\":531595,\"port2\":\"previous\"},{\"id1\":139555,\"port1\":\"character\",\"id2\":432049,\"port2\":\"targets\"},{\"id1\":139555,\"port1\":\"character\",\"id2\":531595,\"port2\":\"targets\"},{\"id1\":765106,\"port1\":\"result\",\"id2\":617363,\"port2\":\"width\"},{\"id1\":765106,\"port1\":\"result\",\"id2\":290804,\"port2\":\"a\"},{\"id1\":114418,\"port1\":\"magnitude\",\"id2\":765106,\"port2\":\"value\"},{\"id1\":114418,\"port1\":\"magnitude\",\"id2\":765106,\"port2\":\"max\"},{\"id1\":617363,\"port1\":\"characters\",\"id2\":819354,\"port2\":\"collectionPort\"},{\"id1\":726775,\"port1\":\"character\",\"id2\":617363,\"port2\":\"source\"},{\"id1\":834685,\"port1\":\"value\",\"id2\":617363,\"port2\":\"height\"},{\"id1\":162039,\"port1\":\"result\",\"id2\":617363,\"port2\":\"offsetX\"},{\"id1\":912583,\"port1\":\"result\",\"id2\":617363,\"port2\":\"offsetY\"},{\"id1\":125611,\"port1\":\"directionInDegree\",\"id2\":617363,\"port2\":\"angle\"},{\"id1\":819354,\"port1\":\"countPort\",\"id2\":312039,\"port2\":\"a\"},{\"id1\":598809,\"port1\":\"x\",\"id2\":162039,\"port2\":\"a\"},{\"id1\":933724,\"port1\":\"x\",\"id2\":162039,\"port2\":\"b\"},{\"id1\":598809,\"port1\":\"y\",\"id2\":912583,\"port2\":\"a\"},{\"id1\":433755,\"port1\":\"result\",\"id2\":598809,\"port2\":\"value\"},{\"id1\":933724,\"port1\":\"y\",\"id2\":912583,\"port2\":\"b\"},{\"id1\":237163,\"port1\":\"center\",\"id2\":933724,\"port2\":\"value\"},{\"id1\":941799,\"port1\":\"character\",\"id2\":237163,\"port2\":\"target\"},{\"id1\":125611,\"port1\":\"directionInDegree\",\"id2\":267142,\"port2\":\"a\"},{\"id1\":382587,\"port1\":\"result\",\"id2\":125611,\"port2\":\"vector\"},{\"id1\":382587,\"port1\":\"result\",\"id2\":114418,\"port2\":\"vector\"},{\"id1\":635257,\"port1\":\"value\",\"id2\":290804,\"port2\":\"b\"},{\"id1\":661649,\"port1\":\"x\",\"id2\":306494,\"port2\":\"a\"},{\"id1\":596821,\"port1\":\"x\",\"id2\":306494,\"port2\":\"b\"},{\"id1\":661649,\"port1\":\"y\",\"id2\":458012,\"port2\":\"a\"},{\"id1\":596821,\"port1\":\"y\",\"id2\":458012,\"port2\":\"b\"},{\"id1\":493074,\"port1\":\"character\",\"id2\":494892,\"port2\":\"targets\"},{\"id1\":439693,\"port1\":\"next\",\"id2\":771563,\"port2\":\"previous\"},{\"id1\":439693,\"port1\":\"self\",\"id2\":771563,\"port2\":\"targets\"},{\"id1\":441190,\"port1\":\"cooldownTicks\",\"id2\":171083,\"port2\":\"a\"},{\"id1\":171083,\"port1\":\"result\",\"id2\":766002,\"port2\":\"cooldownTicks\"}]}"
