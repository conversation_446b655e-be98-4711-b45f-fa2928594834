fileFormatVersion: 2
guid: 4a6bdcfaf578e3145bce436abc920588
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 0
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Foreground
      rect:
        serializedVersion: 2
        x: 1
        y: 1312
        width: 514
        height: 735
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7459779d30bd15c49a7822e6ccf097c9
      internalID: 1480378641
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Front
      rect:
        serializedVersion: 2
        x: 1
        y: 575
        width: 514
        height: 735
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b976a9b4b41f9c6468e1bdcd2a7408b4
      internalID: 1731029816
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Light_1
      rect:
        serializedVersion: 2
        x: 771
        y: 1366
        width: 514
        height: 681
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a000b54715ee43b45b6e2986269be48a
      internalID: 362269432
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Smoke_1
      rect:
        serializedVersion: 2
        x: 517
        y: 627
        width: 344
        height: 600
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a45347ace1c47294fa8c59cc67c9305a
      internalID: -1605392559
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Smoke_2
      rect:
        serializedVersion: 2
        x: 517
        y: 1663
        width: 236
        height: 384
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 26a1feb8c3a3bc84db06d401d6777df7
      internalID: -1979260678
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Smoke_3
      rect:
        serializedVersion: 2
        x: 1
        y: 61
        width: 252
        height: 512
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 68a7d5b05ef40ae45a612788e113739f
      internalID: -1487776612
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Smoke_4
      rect:
        serializedVersion: 2
        x: 255
        y: 149
        width: 514
        height: 424
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5ff0dcd157fcbda44934a570c1bc415e
      internalID: -851560260
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Light_Blue
      rect:
        serializedVersion: 2
        x: 517
        y: 1466
        width: 204
        height: 195
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f2d675fd5d16cb742a4367a038ca33f6
      internalID: -1117742353
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Light_Pink
      rect:
        serializedVersion: 2
        x: 647
        y: 1275
        width: 112
        height: 189
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: af73432c803a3284bb52331e5d459828
      internalID: 1341556576
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Light_Pink_2
      rect:
        serializedVersion: 2
        x: 517
        y: 1229
        width: 128
        height: 235
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b04bc0e6e921f43499ef4b5a87b97b41
      internalID: -1766928116
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 92a4179f750ab3e469a847999c65bfc3
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      Foreground: 1480378641
      Front: 1731029816
      Light_1: 362269432
      Light_Blue: -1117742353
      Light_Pink: 1341556576
      Light_Pink_2: -1766928116
      Smoke_1: -1605392559
      Smoke_2: -1979260678
      Smoke_3: -1487776612
      Smoke_4: -851560260
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
