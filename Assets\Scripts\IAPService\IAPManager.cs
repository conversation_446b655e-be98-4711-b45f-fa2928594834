using System;
using System.Collections.Generic;
using System.Linq;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.Purchasing;

namespace Fanimax.CP.IAP
{
    public enum ProductType { Consumable, NonConsumable, Subscription }

    public class ProductData
    {
        private readonly string _id;
        private readonly ProductType _type;

        public string Id => _id;
        public ProductType Type => _type;

        public ProductData(string id, ProductType type)
        {
            _id = id;
            _type = type;
        }

        internal ProductDefinition ToProductDefinition()
        {
            var unityProductType = _type switch
            {
                ProductType.NonConsumable => UnityEngine.Purchasing.ProductType.NonConsumable,
                ProductType.Subscription => UnityEngine.Purchasing.ProductType.Subscription,
                _ => UnityEngine.Purchasing.ProductType.Consumable,
            };
            return new ProductDefinition(_id, unityProductType);
        }
    }

    /// <remarks>
    /// This IAPManager can only support one purchase at a time.
    /// </remarks>
    public class IAPManager : IIAPManager, IIAPManagerInternal
    {
        private static readonly Logger.Label Label = Logger.Label.CreateFromCurrentClass();

        private readonly IPlayFabManager _playFabManager;
        private readonly IAPStoreListener _storeListener;

        private readonly List<ProductDefinition> _productCatalog = new(8);
        private readonly HashSet<Product> _pendingProducts = new(4);
        private UniTaskCompletionSource<Result> _utcs;
        private bool _enableVerification;

        // 原生平台價格刷新相關
        private UniTaskCompletionSource<bool> _priceRefreshUTCS;

        #if UNITY_IOS && !UNITY_EDITOR
        [System.Runtime.InteropServices.DllImport("__Internal")]
        private static extern void InitializeStoreKitCacheManager();

        [System.Runtime.InteropServices.DllImport("__Internal")]
        private static extern void ForceRefreshStoreKitProducts(string[] productIds, int count, System.IntPtr callback);

        [System.Runtime.InteropServices.DllImport("__Internal")]
        private static extern void ClearStoreKitCache();

        private delegate void StoreKitRefreshCallback(bool success);
        private static StoreKitRefreshCallback _storeKitCallback;
        private static UniTaskCompletionSource<bool> _staticPriceRefreshUTCS;
        #endif

        #if UNITY_ANDROID && !UNITY_EDITOR
        private AndroidJavaClass _googlePlayCacheManager;
        #endif

        public IEnumerable<ProductDefinition> ProductCatalog => _productCatalog;

        public IAPManager(IPlayFabManager playFabManager)
        {
            _playFabManager = playFabManager;
            _storeListener = new IAPStoreListener(this);

            // 初始化原生平台管理器
            InitializeNativePlatformManagers();
        }

        private void InitializeNativePlatformManagers()
        {
            #if UNITY_IOS && !UNITY_EDITOR
            try
            {
                InitializeStoreKitCacheManager();
                Logger.Debug(Label, "iOS StoreKit cache manager initialized");
            }
            catch (System.Exception ex)
            {
                Logger.Error(Label, $"Failed to initialize iOS StoreKit cache manager: {ex.Message}");
            }
            #endif

            #if UNITY_ANDROID && !UNITY_EDITOR
            try
            {
                _googlePlayCacheManager = new AndroidJavaClass("com.fanimax.cp.billing.GooglePlayCacheManager");
                Logger.Debug(Label, "Android Google Play cache manager initialized");
            }
            catch (System.Exception ex)
            {
                Logger.Error(Label, $"Failed to initialize Android Google Play cache manager: {ex.Message}");
            }
            #endif
        }

        public bool IsProcessing()
        {
            return _utcs != null;
        }

        public string GetProductPriceString(string productId)
        {
            if (string.IsNullOrEmpty(productId))
            {
                Logger.Debug(Label, $"GetProductPriceString: productId is null or empty");
                return null;
            }

            if (!IsInitialized())
            {
                Logger.Debug(Label, $"GetProductPriceString: IAP not initialized for product {productId}");
                return null;
            }

            Product internalProduct = _storeListener.GetProduct(productId);
            if (internalProduct == null)
            {
                Logger.Warn(Label, $"GetProductPriceString: Product not found for productId: {productId}");
                return null;
            }

            if (internalProduct.metadata == null)
            {
                Logger.Warn(Label, $"GetProductPriceString: Product metadata is null for productId: {productId}");
                return null;
            }

            string priceString = internalProduct.metadata.localizedPriceString;
            Logger.Debug(Label, $"GetProductPriceString debug info for {productId}:\n  - isoCurrencyCode: {internalProduct.metadata.isoCurrencyCode}\n  - localizedPrice: {internalProduct.metadata.localizedPrice}\n  - localizedTitle: {internalProduct.metadata.localizedTitle}\n  - localizedDescription: {internalProduct.metadata.localizedDescription}");

            if (string.IsNullOrEmpty(priceString))
            {
                Logger.Warn(Label, $"GetProductPriceString: localizedPriceString is null or empty for productId: {productId}");
                return null;
            }

            Logger.Debug(Label, $"GetProductPriceString success for {productId}: {priceString} ({internalProduct.metadata.isoCurrencyCode})");
            

            // Trim .00
            if (priceString.EndsWith(".00"))
            {
                priceString = priceString[..^3];
            }
            else if (priceString.EndsWith(".0"))
            {
                priceString = priceString[..^2];
            }

            return priceString;
        }

        #region Initialize
        public bool IsInitialized()
        {
            return _storeListener.IsInitialized();
        }

        public async UniTask<Result> InitializeAsync(List<ProductData> initialCatalog)
        {
            if (IsProcessing())
            {
                return Result.Failure(ErrorCode.IAPAlreadyProcessing);
            }

            // 每次初始化都強制刷新價格緩存
            Logger.Debug(Label, "Force refreshing platform price cache before initialization...");
            bool refreshSuccess = await ForceRefreshPlatformPricesAsync(initialCatalog);

            if (!refreshSuccess)
            {
                Logger.Warn(Label, "Platform price refresh failed, continuing with normal initialization");
            }

            _productCatalog.Clear();
            for (var i = 0; i < initialCatalog.Count; i++)
            {
                _productCatalog.Add(initialCatalog[i].ToProductDefinition());
            }

            _utcs = new UniTaskCompletionSource<Result>();

            if (!IsInitialized())
            {
                _storeListener.Initialize();
            }else
            {
                OnInitializeSuccess();
            }

            Result result = await _utcs.Task;
            _utcs = null;
            return result;
        }

        public void OnInitializeSuccess()
        {
            Logger.Debug(Label, $"Initialize Success!");
            _utcs?.TrySetResult(Result.Success());
        }

        public void OnInitializeFailed(InitializationFailureReason error, string message)
        {
            Logger.Error(Label, $"Initialize Failed! Reason: {error}\n{message}");
            _utcs?.TrySetResult(Result.Failure(InitializationFailureReasonToErrorCode(error), message: message));
        }

        public void OnStoreDisconnected()
        {
            Logger.Warn(Label, "Store disconnected! This may affect IAP functionality and price display.");
            
            // If there's a pending operation, fail it with a connection error
            if (_utcs != null)
            {
                Logger.Error(Label, "Failing pending IAP operation due to store disconnection");
                _utcs.TrySetResult(Result.Failure(ErrorCode.IAPStoreConnectionLost));
                _utcs = null;
            }
            
            // Clear pending products as they may no longer be valid
            if (_pendingProducts.Count > 0)
            {
                Logger.Warn(Label, $"Clearing {_pendingProducts.Count} pending products due to store disconnection");
                _pendingProducts.Clear();
            }
            
            Logger.Debug(Label, "Store disconnection handling completed");
        }
        #endregion

        #region Force Refresh
        /// <summary>
        /// 強制刷新平台價格緩存並重新初始化 IAP
        /// 用於解決 Apple ID 或 Google 帳號切換後價格顯示錯誤的問題
        /// </summary>
        public async UniTask<Result> ForceRefreshPricesAndReinitializeAsync(List<ProductData> initialCatalog)
        {
            if (IsProcessing())
            {
                return Result.Failure(ErrorCode.IAPAlreadyProcessing);
            }

            Logger.Debug(Label, "Starting force refresh prices and reinitialize...");

            try
            {
                // 1. 先重置當前狀態
                await ResetAsync();

                // 2. 強制刷新平台價格緩存
                bool refreshSuccess = await ForceRefreshPlatformPricesAsync(initialCatalog);

                if (!refreshSuccess)
                {
                    Logger.Warn(Label, "Platform price refresh failed, but continuing with initialization");
                }

                // 3. 重新初始化
                _productCatalog.Clear();
                for (var i = 0; i < initialCatalog.Count; i++)
                {
                    _productCatalog.Add(initialCatalog[i].ToProductDefinition());
                }

                _utcs = new UniTaskCompletionSource<Result>();
                _storeListener.Initialize();

                Result result = await _utcs.Task;
                _utcs = null;

                Logger.Debug(Label, $"Force refresh and reinitialize completed with result: {result.IsSuccess}");
                return result;
            }
            catch (System.Exception ex)
            {
                Logger.Error(Label, $"Force refresh and reinitialize failed: {ex.Message}");
                return Result.Failure(ErrorCode.IAPUnknownError, message: ex.Message);
            }
        }
        #endregion

        #region Purchase
        public async UniTask<Result> PurchaseAsync(string productId)
        {
            if (!IsInitialized())
            {
                Logger.Error(Label, "StartPurchase Failed! IAPService is not initialized.");
                return Result.Failure(ErrorCode.IAPNotInitialized);
            }

            if (IsProcessing())
            {
                Logger.Error(Label, $"StartPurchase Failed! There is already another purchase in progress.");
                return Result.Failure(ErrorCode.IAPAlreadyProcessing);
            }

            Product product = _storeListener.GetProduct(productId);
            if (product == null)
            {
                Logger.Error(Label, $"StartPurchase Failed! Product not found. ProductId: {productId}");
                return Result.Failure(ErrorCode.IAPProductNotFound, message: $"productId: {productId}");
            }

            if (!product.availableToPurchase)
            {
                Logger.Error(Label, $"StartPurchase Failed! Product is not available to purchase. ProductId: {productId}");
                return Result.Failure(ErrorCode.IAPProductUnavailable, message: $"productId: {productId}");
            }

            _utcs = new UniTaskCompletionSource<Result>();
            _storeListener.InitiatePurchase(product);

            Result result = await _utcs.Task;
            _utcs = null;
            return result;
        }

        public void ProcessPurchase(Product product)
        {
            Logger.Debug(Label, $"Purchase Success Step 1! ProductId: {product.definition.id}");
            _pendingProducts.Add(product);
            if (_enableVerification)
            {
                VerifyReceiptAsync(product).Forget();
            }
        }

        public void OnPurchaseFailed(Product product, PurchaseFailureDescription failureDescription)
        {
            var message = $"ProductId: {product.definition.id}\nReason: {failureDescription.reason}\n{failureDescription.message}";
            Logger.Error(Label, $"Purchase Failed! {message}");
            _utcs?.TrySetResult(Result.Failure(PurchaseFailureReasonToErrorCode(failureDescription.reason), message: message));
        }
        #endregion

        #region Error Code
        private static ErrorCode InitializationFailureReasonToErrorCode(InitializationFailureReason reason)
        {
            return reason switch
            {
                InitializationFailureReason.PurchasingUnavailable => ErrorCode.IAPPurchasingUnavailable,
                InitializationFailureReason.NoProductsAvailable => ErrorCode.IAPNoProductsAvailable,
                InitializationFailureReason.AppNotKnown => ErrorCode.IAPAppNotKnown,
                _ => ErrorCode.IAPUnknownError,
            };
        }

        private static ErrorCode PurchaseFailureReasonToErrorCode(PurchaseFailureReason reason)
        {
            return reason switch
            {
                PurchaseFailureReason.PurchasingUnavailable => ErrorCode.IAPPurchasingUnavailable,
                PurchaseFailureReason.ExistingPurchasePending => ErrorCode.IAPExistingPurchasePending,
                PurchaseFailureReason.ProductUnavailable => ErrorCode.IAPProductUnavailable,
                PurchaseFailureReason.SignatureInvalid => ErrorCode.IAPSignatureInvalid,
                PurchaseFailureReason.UserCancelled => ErrorCode.IAPUserCancelled,
                PurchaseFailureReason.PaymentDeclined => ErrorCode.IAPPaymentDeclined,
                PurchaseFailureReason.DuplicateTransaction => ErrorCode.IAPDuplicateTransaction,
                _ => ErrorCode.IAPPurchaseFailedUnknown,
            };
        }
        #endregion

        #region Verify Receipt
        public async UniTask<Result> ContinuePendingPurchaseAsync()
        {
            if (!IsInitialized())
            {
                return Result.Failure(ErrorCode.IAPNotInitialized);
            }

            if (IsProcessing())
            {
                return Result.Failure(ErrorCode.IAPAlreadyProcessing);
            }

            // Wait for a while to get pending products after OnInitialized(),
            // which is just another synchronization method at next line, so it's safe to wait.
            await UniTask.Delay(1000);

            _enableVerification = true;
            Logger.Debug(Label, $"ContinuePendingPurchase! PendingProduct Count = {_pendingProducts.Count}");

            if (_pendingProducts.Count == 0)
            {
                return Result.Success();
            }

            _utcs = new UniTaskCompletionSource<Result>();

            // Note: Clone to prevent ConcurrentModificationException.
            HashSet<Product> pendingProductsClone = new(_pendingProducts);
            foreach (Product product in pendingProductsClone)
            {
                VerifyReceiptAsync(product).Forget();
            }

            Result result = await _utcs.Task;
            _utcs = null;
            return result;
        }

        private async UniTaskVoid VerifyReceiptAsync(Product product)
        {
            Logger.Debug(Label, $"VerifyReceipt! ProductId: {product.definition.id}\n" +
                $"TransactionID: {product.transactionID}\n" +
                $"Receipt: {product.receipt}");

            VerifyReceiptRequest request = CreateVerifyReceiptRequest(product);
            if (request == null)
            {
                _utcs?.TrySetResult(Result.Failure(ErrorCode.IAPVerifyReceiptFailed));
                return;
            }

            Result<VerifyReceiptResponse> result = await _playFabManager.Execute(request, new FunctionExecuteSetting
            {
                customFailureHandler = (Error error) => true
            });

            _ = _pendingProducts.Remove(product);
            if (result.IsSuccess)
            {
                _storeListener.ConfirmPendingPurchase(product);
                if (_pendingProducts.Count == 0)
                {
                    _utcs?.TrySetResult(Result.Success());
                }
            }
            else
            {
                _utcs?.TrySetResult(Result.Failure(result.Error));
            }
        }

        /// <summary>
        /// 強制刷新平台價格緩存 (iOS StoreKit / Android Google Play)
        /// </summary>
        private async UniTask<bool> ForceRefreshPlatformPricesAsync(List<ProductData> productCatalog)
        {
            try
            {
                string[] productIds = productCatalog.Select(p => p.Id).ToArray();

                #if UNITY_IOS && !UNITY_EDITOR
                return await ForceRefreshiOSPricesAsync(productIds);
                #elif UNITY_ANDROID && !UNITY_EDITOR
                return await ForceRefreshAndroidPricesAsync(productIds);
                #else
                Logger.Debug(Label, "Platform price refresh not available in editor");
                return true;
                #endif
            }
            catch (System.Exception ex)
            {
                Logger.Error(Label, $"Force refresh platform prices failed: {ex.Message}");
                return false;
            }
        }

        #if UNITY_IOS && !UNITY_EDITOR
        private async UniTask<bool> ForceRefreshiOSPricesAsync(string[] productIds)
        {
            try
            {
                Logger.Debug(Label, $"Force refreshing iOS StoreKit prices for {productIds.Length} products");

                // 先清除緩存
                ClearStoreKitCache();

                // 等待緩存清除完成
                await UniTask.Delay(500);

                // 設置靜態回調 (因為原生回調必須是靜態的)
                _staticPriceRefreshUTCS = new UniTaskCompletionSource<bool>();
                _storeKitCallback = OnStoreKitRefreshComplete;

                // 調用原生方法強制刷新
                System.IntPtr callbackPtr = System.Runtime.InteropServices.Marshal.GetFunctionPointerForDelegate(_storeKitCallback);
                ForceRefreshStoreKitProducts(productIds, productIds.Length, callbackPtr);

                // 等待刷新完成，最多等待 10 秒
                bool success = await _staticPriceRefreshUTCS.Task.Timeout(System.TimeSpan.FromSeconds(10));

                Logger.Debug(Label, $"iOS price refresh completed with success: {success}");
                return success;
            }
            catch (System.Exception ex)
            {
                Logger.Error(Label, $"iOS price refresh failed: {ex.Message}");
                return false;
            }
            finally
            {
                _staticPriceRefreshUTCS = null;
                _storeKitCallback = null;
            }
        }

        [AOT.MonoPInvokeCallback(typeof(StoreKitRefreshCallback))]
        private static void OnStoreKitRefreshComplete(bool success)
        {
            Logger.Debug(Label, $"StoreKit refresh callback received: {success}");

            if (_staticPriceRefreshUTCS != null)
            {
                _staticPriceRefreshUTCS.TrySetResult(success);
            }
        }
        #endif

        #if UNITY_ANDROID && !UNITY_EDITOR
        private async UniTask<bool> ForceRefreshAndroidPricesAsync(string[] productIds)
        {
            try
            {
                Logger.Debug(Label, $"Force refreshing Android Google Play prices for {productIds.Length} products");

                if (_googlePlayCacheManager == null)
                {
                    Logger.Error(Label, "Google Play cache manager not initialized");
                    return false;
                }

                // 調用 Android 原生方法
                _googlePlayCacheManager.CallStatic("forceRefreshPrices", new object[] { productIds });

                // 等待一段時間讓 Android 處理緩存清除
                await UniTask.Delay(2000);

                Logger.Debug(Label, "Android price refresh completed");
                return true;
            }
            catch (System.Exception ex)
            {
                Logger.Error(Label, $"Android price refresh failed: {ex.Message}");
                return false;
            }
        }

        // Unity 消息接收方法 (從 Android 調用)
        public void OnAndroidPriceRefreshComplete(string success)
        {
            bool isSuccess = success == "true";
            Logger.Debug(Label, $"Android price refresh callback received: {isSuccess}");
        }
        #endif

        private static VerifyReceiptRequest CreateVerifyReceiptRequest(Product product)
        {
            var receipt = JsonUtility.FromJson<PurchaseReceipt>(product.receipt);
            return receipt.Store switch
            {
                "fake" => new VerifyReceiptRequest
                {
                    storeType = IAPStoreType.Fake,
                    productId = product.definition.id,
                    transactionId = receipt.TransactionID,
                    payload = receipt.Payload,
                },
                "GooglePlay" => new VerifyReceiptRequest
                {
                    storeType = IAPStoreType.GooglePlay,
                    productId = product.definition.id,
                    transactionId = receipt.TransactionID,
                    payload = receipt.Payload,
                },
                "AppleAppStore" => new VerifyReceiptRequest
                {
                    storeType = IAPStoreType.AppleAppStore,
                    productId = product.definition.id,
                    transactionId = receipt.TransactionID,
                    payload = receipt.Payload,
                },
                _ => null,
            };
        }
        #endregion

        #region Reset
        public async UniTask ResetAsync()
        {
            try
            {
                Logger.Debug(Label, "Starting IAP reset...");

                // 1. 等待進行中的操作完成
                if (IsProcessing())
                {
                    Logger.Debug(Label, "Waiting for pending IAP operations to complete...");
                    try
                    {
                        await UniTask.WaitUntil(() => !IsProcessing())
                            .Timeout(TimeSpan.FromSeconds(5));
                    }
                    catch (TimeoutException)
                    {
                        Logger.Warn(Label, "Timeout waiting for IAP operations, forcing reset");
                        _utcs?.TrySetCanceled();
                        _utcs = null;
                    }
                }

                // 2. 清理內部狀態，但保留 _storeListener
                _productCatalog.Clear();
                _pendingProducts.Clear();
                _utcs = null;
                _enableVerification = false;

                Logger.Debug(Label, "IAP reset completed - ready for re-initialization");
            }
            catch (Exception ex)
            {
                Logger.Error(Label, $"Error during IAP reset: {ex.Message}");
            }
        }
        #endregion
    }
}
