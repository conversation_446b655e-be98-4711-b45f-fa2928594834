fileFormatVersion: 2
guid: f41b8e7cdb07dc74391dc60dc3626867
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 0
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Emoji_007_Kurai_00_R_101
      rect:
        serializedVersion: 2
        x: 256
        y: 384
        width: 128
        height: 128
      alignment: 3
      pivot: {x: 1, y: 1}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c9a6c0821d75c1842becbe75ca6dc3c9
      internalID: -2056374269
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emoji_007_Kurai_00_R_102
      rect:
        serializedVersion: 2
        x: 384
        y: 384
        width: 128
        height: 128
      alignment: 3
      pivot: {x: 1, y: 1}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5e22acb1c6b00a849b9e82764d3b74d1
      internalID: 990657860
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emoji_007_Kurai_00_R_103
      rect:
        serializedVersion: 2
        x: 0
        y: 256
        width: 128
        height: 128
      alignment: 3
      pivot: {x: 1, y: 1}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7ec548d467c46104bab06c8431719f82
      internalID: -964690487
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emoji_007_Kurai_00_R_301
      rect:
        serializedVersion: 2
        x: 128
        y: 256
        width: 128
        height: 128
      alignment: 3
      pivot: {x: 1, y: 1}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 365c00d4a670f9d45b7224eb4c354017
      internalID: -961563704
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emoji_007_Kurai_00_R_302
      rect:
        serializedVersion: 2
        x: 256
        y: 256
        width: 128
        height: 128
      alignment: 3
      pivot: {x: 1, y: 1}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1f356b16c9206394eb9ff3ed6f0847a8
      internalID: -954985727
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emoji_007_Kurai_00_SR_301
      rect:
        serializedVersion: 2
        x: 0
        y: 128
        width: 128
        height: 128
      alignment: 3
      pivot: {x: 1, y: 1}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 38cb1a535db876845a53f27ee05966a2
      internalID: 160400813
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emoji_007_Kurai_00_R_000
      rect:
        serializedVersion: 2
        x: 0
        y: 384
        width: 128
        height: 128
      alignment: 3
      pivot: {x: 1, y: 1}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fbb6f93c1e2402c4792211f6fa4851b3
      internalID: 927630902
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emoji_007_Kurai_00_R_001
      rect:
        serializedVersion: 2
        x: 128
        y: 384
        width: 128
        height: 128
      alignment: 3
      pivot: {x: 1, y: 1}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 07eaf0e5bdaf88d4e94abb0152e0c551
      internalID: -394278120
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emoji_007_Kurai_00_SR_101
      rect:
        serializedVersion: 2
        x: 384
        y: 256
        width: 128
        height: 128
      alignment: 3
      pivot: {x: 1, y: 1}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: adc142bba78cea64284faf0728b3289c
      internalID: -240118848
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: a0fd4e66b562ec647a442cde35e1c51c
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      Emoji_007_Kurai_00_R_000: 927630902
      Emoji_007_Kurai_00_R_001: -394278120
      Emoji_007_Kurai_00_R_101: -2056374269
      Emoji_007_Kurai_00_R_102: 990657860
      Emoji_007_Kurai_00_R_103: -964690487
      Emoji_007_Kurai_00_R_301: -961563704
      Emoji_007_Kurai_00_R_302: -954985727
      Emoji_007_Kurai_00_SR_101: -240118848
      Emoji_007_Kurai_00_SR_301: 160400813
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
