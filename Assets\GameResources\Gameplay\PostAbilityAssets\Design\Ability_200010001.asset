%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45f4116f2cdfba14b8cf00f26426a3d4, type: 3}
  m_Name: Ability_200010001
  m_EditorClassIdentifier: 
  blackboard:
  - key: speed
    value: 0
  - key: rangeX
    value: 0
  - key: lifetime
    value: 0
  - key: stun
    value: 0
  - key: energyRecovery
    value: 0
  - key: test
    value: 0
  graphGroups:
  - graphs:
    - nodes:
      - rid: 8923418969171559150
      - rid: 8923418969171559151
      - rid: 8923418969171559152
      - rid: 8923418969171559153
      - rid: 8923418969171559154
      - rid: 8923418969171559155
      - rid: 8923418969171559156
      - rid: 8923418969171559157
      - rid: 8923418969171559158
      - rid: 8923418969171559159
      - rid: 8923418969171559160
      - rid: 8923418969171559161
      - rid: 8923418969171559162
      - rid: 8923418969171559163
      - rid: 8923418969171559164
      - rid: 8923418969171559165
      - rid: 8923418969171559166
      - rid: 8923418969171559167
      - rid: 8923418969171559168
      - rid: 8923418969171559169
      - rid: 8923418969171559170
      - rid: 8923418969171559171
      - rid: 8923418969171559172
      - rid: 8923418969171559173
      - rid: 8923418969171559174
      - rid: 8923418969171559175
      - rid: 8923418969171559176
      - rid: 8923418969171559177
      - rid: 8923418969171559178
      - rid: 8923418969171559179
      - rid: 8923418969171559180
      - rid: 8923418969171559181
      - rid: 8923418969171559182
      edges:
      - id1: 410777
        port1: next
        id2: 108378
        port2: previous
      - id1: 489041
        port1: value
        id2: 410777
        port2: size
      - id1: 591797
        port1: value
        id2: 410777
        port2: speed
      - id1: 274049
        port1: value
        id2: 410777
        port2: stunTicks
      - id1: 282919
        port1: value
        id2: 410777
        port2: lifetime
      - id1: 444208
        port1: value
        id2: 410777
        port2: energyGainForHit
      - id1: 862389
        port1: next
        id2: 680458
        port2: previousPort
      - id1: 862389
        port1: level
        id2: 848641
        port2: a
      - id1: 108378
        port1: next
        id2: 801772
        port2: previous
      - id1: 993607
        port1: character
        id2: 108378
        port2: targets
      - id1: 801772
        port1: next
        id2: 211525
        port2: previous
      - id1: 368956
        port1: character
        id2: 801772
        port2: targets
      - id1: 680458
        port1: truePort
        id2: 410777
        port2: previous
      - id1: 680458
        port1: falsePort
        id2: 450117
        port2: previous
      - id1: 826125
        port1: character
        id2: 410777
        port2: caster
      - id1: 848641
        port1: result
        id2: 680458
        port2: conditionPort
      - id1: 450117
        port1: next
        id2: 129827
        port2: previous
      - id1: 855391
        port1: value
        id2: 450117
        port2: size
      - id1: 636939
        port1: value
        id2: 450117
        port2: speed
      - id1: 903626
        port1: value
        id2: 450117
        port2: stunTicks
      - id1: 508856
        port1: value
        id2: 450117
        port2: lifetime
      - id1: 22047
        port1: value
        id2: 450117
        port2: energyGainForHit
      - id1: 534073
        port1: character
        id2: 450117
        port2: caster
      - id1: 129827
        port1: next
        id2: 715213
        port2: previous
      - id1: 981152
        port1: character
        id2: 129827
        port2: targets
      - id1: 715213
        port1: next
        id2: 649152
        port2: previous
      - id1: 925784
        port1: character
        id2: 715213
        port2: targets
      - id1: 211525
        port1: next
        id2: 915574
        port2: previous
      - id1: 444626
        port1: character
        id2: 211525
        port2: instigator
      - id1: 41929
        port1: character
        id2: 649152
        port2: instigator
      - id1: 649152
        port1: next
        id2: 129367
        port2: previous
      - id1: 694538
        port1: value
        id2: 410777
        port2: size2
      - id1: 569626
        port1: value
        id2: 450117
        port2: size2
  - graphs:
    - nodes:
      - rid: 8923418969171559183
      - rid: 8923418969171559184
      - rid: 8923418969171559185
      - rid: 8923418969171559186
      edges:
      - id1: 100388
        port1: next
        id2: 111056
        port2: previous
      - id1: 100388
        port1: instigator
        id2: 111056
        port2: instigator
      - id1: 100388
        port1: target
        id2: 111056
        port2: targets
      - id1: 187970
        port1: next
        id2: 523851
        port2: previous
      - id1: 187970
        port1: caster
        id2: 523851
        port2: instigator
  references:
    version: 2
    RefIds:
    - rid: 8923418969171559150
      type: {class: ProjectileNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 410777
        size: 0
        size2: 0
        rotation: 0
        offsetX: 0
        offsetY: -5000
        directionInDegree: 0
        speed: 0
        faceDirection: 0
        lifetime: 0
        stunTicks: 0
        energyGainForHit: 0
        energyGainForBreak: 0
        isPenetrate: 0
        hitPeriodTick: 0
        gameplayAbilityId: -1
        groupIndex: 1
        vfxDataId: 30001011
        hitFxDataId: -2
        canHitLevelTrap: 0
        canBreakLevelTrap: 1
        ignoreHitIfPreviousHit: 0
        isFRKRule: 0
        shapeType: 2
        pathType: 0
        rootType: 1
        pathArg1: 0
        pathArg2: 0
        pathArg3: 0
    - rid: 8923418969171559151
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 489041
        key: rangeX
    - rid: 8923418969171559152
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 591797
        key: speed
    - rid: 8923418969171559153
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 274049
        key: stun
    - rid: 8923418969171559154
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 282919
        key: lifetime
    - rid: 8923418969171559155
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 444208
        key: energyRecovery
    - rid: 8923418969171559156
      type: {class: FinishNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 915574
    - rid: 8923418969171559157
      type: {class: OnActivatedData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 862389
        ignoreStun: 0
    - rid: 8923418969171559158
      type: {class: AddCameraBumpByActorNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 108378
        ownerOnly: 1
        bumpId: -2
    - rid: 8923418969171559159
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 993607
    - rid: 8923418969171559160
      type: {class: AddCameraShakeByActorNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 801772
        ownerOnly: 1
        shakeId: -2
    - rid: 8923418969171559161
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 368956
    - rid: 8923418969171559162
      type: {class: IfElseNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 680458
        conditionPort: 0
    - rid: 8923418969171559163
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 826125
    - rid: 8923418969171559164
      type: {class: EqualNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 848641
        a: 0
        b: 1
    - rid: 8923418969171559165
      type: {class: ProjectileNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 450117
        size: 0
        size2: 0
        rotation: 0
        offsetX: 0
        offsetY: -5000
        directionInDegree: 0
        speed: 0
        faceDirection: 0
        lifetime: 0
        stunTicks: 0
        energyGainForHit: 0
        energyGainForBreak: 0
        isPenetrate: 0
        hitPeriodTick: 0
        gameplayAbilityId: -1
        groupIndex: 2
        vfxDataId: 30001012
        hitFxDataId: -2
        canHitLevelTrap: 0
        canBreakLevelTrap: 1
        ignoreHitIfPreviousHit: 0
        isFRKRule: 0
        shapeType: 2
        pathType: 0
        rootType: 1
        pathArg1: 0
        pathArg2: 0
        pathArg3: 0
    - rid: 8923418969171559166
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 855391
        key: rangeX0
    - rid: 8923418969171559167
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 636939
        key: speed0
    - rid: 8923418969171559168
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 903626
        key: stun
    - rid: 8923418969171559169
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 508856
        key: lifetime
    - rid: 8923418969171559170
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 22047
        key: energyRecovery
    - rid: 8923418969171559171
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 534073
    - rid: 8923418969171559172
      type: {class: AddCameraBumpByActorNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 129827
        ownerOnly: 1
        bumpId: -2
    - rid: 8923418969171559173
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 981152
    - rid: 8923418969171559174
      type: {class: AddCameraShakeByActorNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 715213
        ownerOnly: 1
        shakeId: -2
    - rid: 8923418969171559175
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 925784
    - rid: 8923418969171559176
      type: {class: FinishNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 129367
    - rid: 8923418969171559177
      type: {class: AbilityLogNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 211525
        eventType: 1
        param1: 1
        param2: 0
        param3: 0
        param4: 0
    - rid: 8923418969171559178
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 444626
    - rid: 8923418969171559179
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 41929
    - rid: 8923418969171559180
      type: {class: AbilityLogNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 649152
        eventType: 1
        param1: 0
        param2: 1
        param3: 0
        param4: 0
    - rid: 8923418969171559181
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 694538
        key: rangeY
    - rid: 8923418969171559182
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 569626
        key: rangeY0
    - rid: 8923418969171559183
      type: {class: OnProjectileApplyStunNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 100388
    - rid: 8923418969171559184
      type: {class: AbilityLogNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 111056
        eventType: 0
        param1: 1
        param2: 0
        param3: 0
        param4: 0
    - rid: 8923418969171559185
      type: {class: OnObjectBreakOtherData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 187970
    - rid: 8923418969171559186
      type: {class: AbilityLogNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 523851
        eventType: 0
        param1: 0
        param2: 1
        param3: 0
        param4: 0
