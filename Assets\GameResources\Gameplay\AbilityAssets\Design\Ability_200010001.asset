%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 32f4e526f6c4d2a49979457203108d7a, type: 3}
  m_Name: Ability_200010001
  m_EditorClassIdentifier: 
  blackboard:
  - key: speed
    value: 0
  - key: rangeX
    value: 0
  - key: lifetime
    value: 0
  - key: stun
    value: 0
  - key: energyRecovery
    value: 0
  - key: test
    value: 0
  graphJsons:
  - '{"_type":"Physalia.Flexi.AbilityGraph","nodes":[{"_id":775246,"_position":{"x":-44.0,"y":211.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.OnCastNode"},{"_id":145346,"_position":{"x":350.0,"y":213.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.AppendBoostNode"},{"_id":611588,"_position":{"x":157.0,"y":508.0},"_type":"Physalia.Flexi.BlackboardNode","key":"duration"},{"_id":60027,"_position":{"x":140.0,"y":410.0},"_type":"Physalia.Flexi.BlackboardNode","key":"boostAccel"},{"_id":478235,"_position":{"x":136.0,"y":312.0},"_type":"Physalia.Flexi.BlackboardNode","key":"boostSpeed"},{"_id":465553,"_position":{"x":128.0,"y":110.0},"_type":"Physalia.Flexi.IfElseNode"}],"edges":[{"id1":775246,"port1":"next","id2":465553,"port2":"previousPort"},{"id1":775246,"port1":"caster","id2":145346,"port2":"targets"},{"id1":465553,"port1":"truePort","id2":145346,"port2":"previous"},{"id1":478235,"port1":"value","id2":145346,"port2":"initialSpeed"},{"id1":478235,"port1":"value","id2":145346,"port2":"speedMaxModifier"},{"id1":60027,"port1":"value","id2":145346,"port2":"accelerationModifier"},{"id1":611588,"port1":"value","id2":145346,"port2":"duration"}]}'
  graphGroups:
  - graphs:
    - '{"_type":"Physalia.Flexi.AbilityGraph","nodes":[{"_id":410777,"_position":{"x":1419.37781,"y":-133.3053},"_type":"Fanimax.CP.Gameplay.AbilityServices.ProjectileNode","offsetY":-5000,"lifetime":50000,"gameplayAbilityId":-1,"groupIndex":1,"vfxDataId":30001011,"hitFxDataId":-2,"canHitLevelTrap":false,"canBreakLevelTrap":true,"ignoreHitIfPreviousHit":false,"isFRKRule":false,"shapeType":2,"pathType":0,"rootType":1,"pathArg1":0,"pathArg2":0,"pathArg3":0},{"_id":489041,"_position":{"x":1203.37781,"y":-10.3052979},"_type":"Physalia.Flexi.BlackboardNode","key":"rangeX"},{"_id":591797,"_position":{"x":1202.37781,"y":87.6947},"_type":"Physalia.Flexi.BlackboardNode","key":"speed"},{"_id":274049,"_position":{"x":1202.37781,"y":281.6947},"_type":"Physalia.Flexi.BlackboardNode","key":"stun"},{"_id":282919,"_position":{"x":1202.37781,"y":183.6947},"_type":"Physalia.Flexi.BlackboardNode","key":"lifetime"},{"_id":444208,"_position":{"x":1149.37781,"y":379.6947},"_type":"Physalia.Flexi.BlackboardNode","key":"energyRecovery"},{"_id":915574,"_position":{"x":2675.378,"y":-133.3053},"_type":"Fanimax.CP.Gameplay.AbilityServices.FinishNode"},{"_id":862389,"_position":{"x":382.3778,"y":-133.3053},"_type":"Fanimax.CP.Gameplay.AbilityServices.OnActivated"},{"_id":108378,"_position":{"x":1805.37781,"y":-133.3053},"_type":"Fanimax.CP.Gameplay.AbilityServices.AddCameraBumpByActorNode","ownerOnly":true,"bumpId":-2},{"_id":993607,"_position":{"x":1653.37781,"y":46.6947021},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":801772,"_position":{"x":2118.378,"y":-133.3053},"_type":"Fanimax.CP.Gameplay.AbilityServices.AddCameraShakeByActorNode","ownerOnly":true,"shakeId":-2},{"_id":368956,"_position":{"x":1956.37781,"y":46.6947021},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":680458,"_position":{"x":1022.37781,"y":-133.3053},"_type":"Physalia.Flexi.IfElseNode"},{"_id":826125,"_position":{"x":1233.37781,"y":-171.3053},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":848641,"_position":{"x":831.3778,"y":-54.3052979},"_type":"Physalia.Flexi.EqualNode","b":1},{"_id":450117,"_position":{"x":1423.0,"y":635.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.ProjectileNode","offsetY":-5000,"gameplayAbilityId":-1,"groupIndex":2,"vfxDataId":30001012,"hitFxDataId":-2,"canHitLevelTrap":false,"canBreakLevelTrap":true,"ignoreHitIfPreviousHit":false,"isFRKRule":false,"shapeType":2,"pathType":0,"rootType":1,"pathArg1":0,"pathArg2":0,"pathArg3":0},{"_id":855391,"_position":{"x":1203.37781,"y":639.6947},"_type":"Physalia.Flexi.BlackboardNode","key":"rangeX0"},{"_id":636939,"_position":{"x":1202.37781,"y":737.6947},"_type":"Physalia.Flexi.BlackboardNode","key":"speed0"},{"_id":903626,"_position":{"x":1202.37781,"y":931.6947},"_type":"Physalia.Flexi.BlackboardNode","key":"stun"},{"_id":508856,"_position":{"x":1202.37781,"y":833.6947},"_type":"Physalia.Flexi.BlackboardNode","key":"lifetime"},{"_id":22047,"_position":{"x":1149.37781,"y":1029.6947},"_type":"Physalia.Flexi.BlackboardNode","key":"energyRecovery"},{"_id":534073,"_position":{"x":1232.37781,"y":562.6947},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":129827,"_position":{"x":1805.37781,"y":516.6947},"_type":"Fanimax.CP.Gameplay.AbilityServices.AddCameraBumpByActorNode","ownerOnly":true,"bumpId":-2},{"_id":981152,"_position":{"x":1653.37781,"y":696.6947},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":715213,"_position":{"x":2118.378,"y":516.6947},"_type":"Fanimax.CP.Gameplay.AbilityServices.AddCameraShakeByActorNode","ownerOnly":true,"shakeId":-2},{"_id":925784,"_position":{"x":1956.37781,"y":696.6947},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":129367,"_position":{"x":2675.378,"y":516.6947},"_type":"Fanimax.CP.Gameplay.AbilityServices.FinishNode"},{"_id":211525,"_position":{"x":2460.378,"y":-21.3052979},"_type":"Fanimax.CP.Gameplay.AbilityServices.AbilityLogNode","eventType":1,"param1":1},{"_id":444626,"_position":{"x":2261.378,"y":46.6947021},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":41929,"_position":{"x":2261.378,"y":696.6947},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":649152,"_position":{"x":2460.378,"y":651.6947},"_type":"Fanimax.CP.Gameplay.AbilityServices.AbilityLogNode","eventType":1,"param2":1},{"_id":694538,"_position":{"x":1010.0,"y":99.0},"_type":"Physalia.Flexi.BlackboardNode","key":"rangeY"},{"_id":569626,"_position":{"x":1027.0,"y":766.0},"_type":"Physalia.Flexi.BlackboardNode","key":"rangeY0"}],"edges":[{"id1":410777,"port1":"next","id2":108378,"port2":"previous"},{"id1":680458,"port1":"truePort","id2":410777,"port2":"previous"},{"id1":826125,"port1":"character","id2":410777,"port2":"caster"},{"id1":489041,"port1":"value","id2":410777,"port2":"size"},{"id1":694538,"port1":"value","id2":410777,"port2":"size2"},{"id1":591797,"port1":"value","id2":410777,"port2":"speed"},{"id1":282919,"port1":"value","id2":410777,"port2":"lifetime"},{"id1":274049,"port1":"value","id2":410777,"port2":"stunTicks"},{"id1":444208,"port1":"value","id2":410777,"port2":"energyGainForHit"},{"id1":108378,"port1":"next","id2":801772,"port2":"previous"},{"id1":993607,"port1":"character","id2":108378,"port2":"targets"},{"id1":801772,"port1":"next","id2":211525,"port2":"previous"},{"id1":368956,"port1":"character","id2":801772,"port2":"targets"},{"id1":211525,"port1":"next","id2":915574,"port2":"previous"},{"id1":444626,"port1":"character","id2":211525,"port2":"instigator"},{"id1":680458,"port1":"falsePort","id2":450117,"port2":"previous"},{"id1":862389,"port1":"next","id2":680458,"port2":"previousPort"},{"id1":848641,"port1":"result","id2":680458,"port2":"conditionPort"},{"id1":450117,"port1":"next","id2":129827,"port2":"previous"},{"id1":534073,"port1":"character","id2":450117,"port2":"caster"},{"id1":855391,"port1":"value","id2":450117,"port2":"size"},{"id1":569626,"port1":"value","id2":450117,"port2":"size2"},{"id1":636939,"port1":"value","id2":450117,"port2":"speed"},{"id1":508856,"port1":"value","id2":450117,"port2":"lifetime"},{"id1":903626,"port1":"value","id2":450117,"port2":"stunTicks"},{"id1":22047,"port1":"value","id2":450117,"port2":"energyGainForHit"},{"id1":129827,"port1":"next","id2":715213,"port2":"previous"},{"id1":981152,"port1":"character","id2":129827,"port2":"targets"},{"id1":715213,"port1":"next","id2":649152,"port2":"previous"},{"id1":925784,"port1":"character","id2":715213,"port2":"targets"},{"id1":649152,"port1":"next","id2":129367,"port2":"previous"},{"id1":41929,"port1":"character","id2":649152,"port2":"instigator"},{"id1":862389,"port1":"level","id2":848641,"port2":"a"}]}'
  - graphs:
    - '{"_type":"Physalia.Flexi.AbilityGraph","nodes":[{"_id":100388,"_position":{"x":474.0,"y":242.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.OnProjectileApplyStunNode"},{"_id":111056,"_position":{"x":857.0,"y":242.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.AbilityLogNode","param1":1},{"_id":187970,"_position":{"x":485.0,"y":583.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.OnObjectBreakOther"},{"_id":523851,"_position":{"x":857.0,"y":583.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.AbilityLogNode","param2":1}],"edges":[{"id1":100388,"port1":"next","id2":111056,"port2":"previous"},{"id1":100388,"port1":"instigator","id2":111056,"port2":"instigator"},{"id1":100388,"port1":"target","id2":111056,"port2":"targets"},{"id1":187970,"port1":"next","id2":523851,"port2":"previous"},{"id1":187970,"port1":"caster","id2":523851,"port2":"instigator"}]}'
