using System.Collections.Generic;
using System.Diagnostics;
using UnityEngine;
using UnityEngine.UI;
using Cysharp.Threading.Tasks;

namespace Fanimax.CP.IAP
{
    /// <summary>
    /// 測試用的 IAP 價格刷新工具
    /// 僅在開發版本中顯示，用於測試 Apple ID 或 Google 帳號切換後的價格刷新功能
    /// </summary>
    public class IAPPriceRefreshTester : MonoBehaviour
    {
        [Header("UI References")]
        [SerializeField] private Button refreshButton;
        [SerializeField] private Text statusText;
        [SerializeField] private Text priceDisplayText;
        
        [Header("Settings")]
        [SerializeField] private bool showInReleaseBuild = false;
        
        private static readonly Logger.Label Label = Logger.Label.CreateFromCurrentClass();
        private IIAPManager _iapManager;
        private bool _isRefreshing = false;

        private void Awake()
        {
            // 只在開發版本或明確設置時顯示
            #if !DEVELOPMENT_BUILD && !UNITY_EDITOR
            if (!showInReleaseBuild)
            {
                gameObject.SetActive(false);
                return;
            }
            #endif

            if (refreshButton != null)
            {
                refreshButton.onClick.AddListener(OnRefreshButtonClickedWrapper);
            }
        }

        private void Start()
        {
            try
            {
                _iapManager = ServiceLocator.Resolve<IIAPManager>();
                UpdateUI();
            }
            catch (System.Exception ex)
            {
                Logger.Error(Label, $"Failed to initialize IAP Price Refresh Tester: {ex.Message}");
                if (statusText != null)
                {
                    statusText.text = "初始化失敗";
                }
            }
        }

        [Conditional("DEVELOPMENT_BUILD")]
        [Conditional("UNITY_EDITOR")]
        private async void OnRefreshButtonClicked()
        {
            if (_isRefreshing)
            {
                Logger.Debug(Label, "Refresh already in progress, ignoring button click");
                return;
            }

            if (_iapManager == null)
            {
                Logger.Error(Label, "IAP Manager not available");
                UpdateStatusText("IAP Manager 未可用");
                return;
            }

            await PerformPriceRefreshAsync();
        }

        /// <summary>
        /// Button onClick event wrapper - 不使用 Conditional 屬性以避免委託創建問題
        /// </summary>
        private void OnRefreshButtonClickedWrapper()
        {
            #if DEVELOPMENT_BUILD || UNITY_EDITOR
            OnRefreshButtonClicked();
            #else
            Logger.Debug(Label, "Refresh button clicked in release build - ignored");
            #endif
        }

        private async UniTask PerformPriceRefreshAsync()
        {
            _isRefreshing = true;
            
            try
            {
                Logger.Debug(Label, "Starting manual price refresh test...");
                UpdateStatusText("正在刷新價格...");
                UpdateButtonState(false);

                // 創建測試用的產品目錄
                var testCatalog = CreateTestCatalog();
                
                if (testCatalog == null || testCatalog.Count == 0)
                {
                    Logger.Error(Label, "Test catalog is empty");
                    UpdateStatusText("測試目錄為空");
                    return;
                }

                // 執行強制刷新
                var result = await _iapManager.ForceRefreshPricesAndReinitializeAsync(testCatalog);

                if (result.IsSuccess)
                {
                    Logger.Debug(Label, "Manual price refresh completed successfully");
                    UpdateStatusText("價格刷新成功！");
                    
                    // 等待一下再更新價格顯示
                    await UniTask.Delay(1000);
                    UpdatePriceDisplay();
                }
                else
                {
                    Logger.Error(Label, $"Manual price refresh failed: {result.Error}");
                    UpdateStatusText($"刷新失敗: {result.Error}");
                }
            }
            catch (System.Exception ex)
            {
                Logger.Error(Label, $"Exception during manual price refresh: {ex.Message}");
                UpdateStatusText($"刷新異常: {ex.Message}");
            }
            finally
            {
                _isRefreshing = false;
                UpdateButtonState(true);
                
                // 5秒後清除狀態文字
                await UniTask.Delay(5000);
                UpdateStatusText("準備就緒");
            }
        }

        private List<ProductData> CreateTestCatalog()
        {
            try
            {
                // 嘗試從 GameDataManager 獲取產品目錄
                var gameDataManager = ServiceLocator.Resolve<IGameDataManager>();
                IReadOnlyList<ProductIdData> dataList = gameDataManager.GetDataList<ProductIdData>();
                
                if (dataList != null && dataList.Count > 0)
                {
                    var catalog = new List<ProductData>(dataList.Count);
                    foreach (var iapData in dataList)
                    {
                        catalog.Add(new ProductData(iapData.ProductId, ProductType.Consumable));
                    }
                    
                    Logger.Debug(Label, $"Created test catalog with {catalog.Count} products");
                    return catalog;
                }
                else
                {
                    Logger.Warn(Label, "ProductIdData table is empty, creating fallback catalog");
                    return CreateFallbackCatalog();
                }
            }
            catch (System.Exception ex)
            {
                Logger.Error(Label, $"Failed to create test catalog: {ex.Message}");
                return CreateFallbackCatalog();
            }
        }

        private List<ProductData> CreateFallbackCatalog()
        {
            // 創建一個基本的測試目錄
            return new List<ProductData>
            {
                new ProductData ("test_product_1", ProductType.Consumable),
                new ProductData ("test_product_2", ProductType.NonConsumable)
            };
        }

        private void UpdateUI()
        {
            UpdateStatusText("準備就緒");
            UpdateButtonState(true);
            UpdatePriceDisplay();
        }

        private void UpdateStatusText(string status)
        {
            if (statusText != null)
            {
                statusText.text = $"狀態: {status}";
            }
        }

        private void UpdateButtonState(bool interactable)
        {
            if (refreshButton != null)
            {
                refreshButton.interactable = interactable;
            }
        }

        private void UpdatePriceDisplay()
        {
            if (priceDisplayText == null || _iapManager == null)
            {
                return;
            }

            try
            {
                var testCatalog = CreateTestCatalog();
                if (testCatalog != null && testCatalog.Count > 0)
                {
                    var priceInfo = new System.Text.StringBuilder();
                    priceInfo.AppendLine("當前價格:");
                    
                    foreach (var product in testCatalog)
                    {
                        string price = _iapManager.GetProductPriceString(product.Id);
                        priceInfo.AppendLine($"{product.Id}: {price ?? "N/A"}");
                    }
                    
                    priceDisplayText.text = priceInfo.ToString();
                }
            }
            catch (System.Exception ex)
            {
                Logger.Error(Label, $"Failed to update price display: {ex.Message}");
                priceDisplayText.text = "價格顯示錯誤";
            }
        }

        private void OnDestroy()
        {
            if (refreshButton != null)
            {
                refreshButton.onClick.RemoveListener(OnRefreshButtonClickedWrapper);
            }
        }
    }
}
