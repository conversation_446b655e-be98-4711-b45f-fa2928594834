%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45f4116f2cdfba14b8cf00f26426a3d4, type: 3}
  m_Name: Ability_2000101
  m_EditorClassIdentifier: 
  blackboard:
  - key: duration
    value: 0
  - key: boostSpeed
    value: 0
  - key: invulnerable
    value: 0
  - key: offsetX
    value: 0
  - key: offsetY
    value: 0
  - key: rangeX
    value: 0
  - key: rangeY
    value: 0
  - key: lifetime
    value: 0
  - key: stun
    value: 0
  - key: energyRecovery
    value: 0
  - key: impactY
    value: 0
  graphGroups:
  - graphs:
    - nodes:
      - rid: 8923418969171559323
      - rid: 8923418969171559324
      - rid: 8923418969171559325
      - rid: 8923418969171559326
      - rid: 8923418969171559327
      - rid: 8923418969171559328
      - rid: 8923418969171559329
      - rid: 8923418969171559330
      - rid: 8923418969171559331
      - rid: 8923418969171559332
      edges:
      - id1: 817886
        port1: next
        id2: 225234
        port2: previous
      - id1: 190302
        port1: value
        id2: 817886
        port2: ticks
      - id1: 771972
        port1: character
        id2: 225234
        port2: targets
      - id1: 225234
        port1: next
        id2: 71643
        port2: previous
      - id1: 71643
        port1: next
        id2: 358694
        port2: previous
      - id1: 740340
        port1: character
        id2: 71643
        port2: targets
      - id1: 541168
        port1: character
        id2: 817886
        port2: targets
      - id1: 652006
        port1: next
        id2: 817886
        port2: previous
      - id1: 358694
        port1: next
        id2: 861515
        port2: previous
  - graphs:
    - nodes:
      - rid: 8923418969171559333
      - rid: 8923418969171559334
      - rid: 8923418969171559335
      - rid: 8923418969171559336
      - rid: 8923418969171559337
      - rid: 8923418969171559338
      - rid: 8923418969171559339
      - rid: 8923418969171559340
      - rid: 8923418969171559341
      - rid: 8923418969171559342
      - rid: 8923418969171559343
      - rid: 8923418969171559344
      - rid: 8923418969171559345
      - rid: 8923418969171559346
      - rid: 8923418969171559347
      - rid: 8923418969171559348
      - rid: 8923418969171559349
      edges:
      - id1: 523310
        port1: next
        id2: 117618
        port2: previous
      - id1: 517379
        port1: value
        id2: 523310
        port2: offsetY
      - id1: 965692
        port1: value
        id2: 523310
        port2: offsetX
      - id1: 313730
        port1: value
        id2: 523310
        port2: size2
      - id1: 76745
        port1: value
        id2: 523310
        port2: size
      - id1: 666721
        port1: character
        id2: 523310
        port2: caster
      - id1: 666721
        port1: character
        id2: 523310
        port2: anchor
      - id1: 615584
        port1: next
        id2: 523310
        port2: previous
      - id1: 452564
        port1: value
        id2: 615584
        port2: ticks
      - id1: 188590
        port1: next
        id2: 615584
        port2: previous
      - id1: 19638
        port1: value
        id2: 523310
        port2: stunTicks
      - id1: 542534
        port1: value
        id2: 523310
        port2: energyGainForHit
      - id1: 539198
        port1: character
        id2: 117618
        port2: targets
      - id1: 738904
        port1: value
        id2: 523310
        port2: lifetime
      - id1: 263426
        port1: next
        id2: 808376
        port2: previous
      - id1: 250012
        port1: character
        id2: 808376
        port2: targets
  references:
    version: 2
    RefIds:
    - rid: 8923418969171559323
      type: {class: AppendAbilityNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 817886
        gameplayAbilityId: -1
        groupIndex: 1
        ticks: 0
        param0: 0
        param1: 0
        param2: 0
        param3: 0
        overwrite: 0
        ignoreClearWhenStunned: 0
        showDurationHUD: 0
    - rid: 8923418969171559324
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 190302
        key: delay
    - rid: 8923418969171559325
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 771972
    - rid: 8923418969171559326
      type: {class: AccelerateToMaxNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 225234
    - rid: 8923418969171559327
      type: {class: FinishNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 861515
    - rid: 8923418969171559328
      type: {class: PlayVfxNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 71643
        ownerOnly: 0
        vfxName: Vfx_CyberItem_ChargeableTrap
        sfxName: 
        degree: 0
        boneType: 1
        customBoneId: 0
        offsetX: 1.75
        offsetY: 3.125
        isInWorld: 0
    - rid: 8923418969171559329
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 740340
    - rid: 8923418969171559330
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 541168
    - rid: 8923418969171559331
      type: {class: OnActivatedData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 652006
        ignoreStun: 0
    - rid: 8923418969171559332
      type: {class: CueCVNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 358694
        abilityCVId: -2
    - rid: 8923418969171559333
      type: {class: CreateAreaNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 523310
        size: 0
        size2: 0
        rotation: 0
        offsetX: 0
        offsetY: 0
        speed: 0
        faceDirection: 0
        lifetime: 0
        deactivationTime: 15
        stunTicks: 0
        energyGainForHit: 0
        canBreakProjectile: 1
        hitPeriodTick: 0
        gameplayAbilityId: 0
        groupIndex: 0
        vfxDataId: 300710001
        hitFxDataId: 3000100
        discardPrevious: 0
        triggerPreviousFinished: 0
        lockX: 0
        lockY: 0
        canHitLevelTrap: 0
        canBreakLevelTrap: 0
        ignoreHitIfPreviousHit: 0
        shapeType: 1
        pathType: 9
        rootType: 0
        pathArg1: 0
        pathArg2: 0
        pathArg3: 0
    - rid: 8923418969171559334
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 517379
        key: offsetY
    - rid: 8923418969171559335
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 965692
        key: offsetX
    - rid: 8923418969171559336
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 313730
        key: rangeY
    - rid: 8923418969171559337
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 76745
        key: rangeX
    - rid: 8923418969171559338
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 666721
    - rid: 8923418969171559339
      type: {class: DelayNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 615584
        ticks: 0
        nextActionTickSlot: 1
        countSlot: 2
    - rid: 8923418969171559340
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 452564
        key: delay
    - rid: 8923418969171559341
      type: {class: OnTickNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 188590
        ignoreStun: 0
        ignoreKnockedDown: 0
    - rid: 8923418969171559342
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 19638
        key: stun
    - rid: 8923418969171559343
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 542534
        key: energyRecovery
    - rid: 8923418969171559344
      type: {class: RemoveAbilityNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 117618
        gameplayAbilityId: -1
        groupIndex: 1
    - rid: 8923418969171559345
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 539198
    - rid: 8923418969171559346
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 738904
        key: lifetime
    - rid: 8923418969171559347
      type: {class: OnTickNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 263426
        ignoreStun: 0
        ignoreKnockedDown: 0
    - rid: 8923418969171559348
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 250012
    - rid: 8923418969171559349
      type: {class: AppendVfxNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 808376
        ownerOnly: 0
        targetsOnly: 0
        vfxName: Vfx_CyberItem_OneShotShield
        sfxName: 
        boneType: 0
        customBoneId: 0
        offsetX: 0
        offsetY: 0
