# This .gitignore file should be placed at the root of your Unity project directory
#
# Get latest from https://github.com/github/gitignore/blob/main/Unity.gitignore
#
/[Ll]ibrary/
/[Tt]emp/
/[Oo]bj/
/[Bb]uild/
/[Bb]uilds/
/[Ll]ogs/
/[Uu]ser[Ss]ettings/

# MemoryCaptures can get excessive in size.
# They also could contain extremely sensitive data
/[Mm]emoryCaptures/

# Recordings can get excessive in size
/[Rr]ecordings/

# Uncomment this line if you wish to ignore the asset store tools plugin
# /[Aa]ssets/AssetStoreTools*

# Autogenerated Jetbrains Rider plugin
/[Aa]ssets/Plugins/Editor/JetBrains*

# Visual Studio cache directory
.vs/
.vscode/

# Visual Studio Code settings
.vscode/settings.json

# Gradle cache directory
.gradle/

# Autogenerated VS/MD/Consulo solution and project files
ExportedObj/
.consulo/
*.csproj
*.unityproj
*.sln
*.suo
*.tmp
*.user
*.userprefs
*.pidb
*.booproj
*.svd
*.pdb
*.mdb
*.opendb
*.VC.db

# Unity3D generated meta files
*.pidb.meta
*.pdb.meta
*.mdb.meta

# Unity3D generated file on crash reports
sysinfo.txt

# Builds
*.apk
*.aab
*.unitypackage
*.app

# Crashlytics generated file
crashlytics-build.properties

# Packed Addressables
/[Aa]ssets/[Aa]ddressable[Aa]ssets[Dd]ata/*/*.bin*

# Temporary auto-generated assets
/[Aa]ssets/[Aa]ddressable[Aa]ssets[Dd]ata/link.xml*
/[Aa]ssets/[Ss]treamingAssets/aa.meta
/[Aa]ssets/[Ss]treamingAssets/aa/*

# Excel Temporary Files
~*.xlsx
~*.xlsm

# PlayFab ExecuteFunction Local Setting
playfab.local.settings.json

# Gameplay Setup Data
SimulatorSetupData.json

# Gameplay Analyze Log
GameplayAnalyze.json

# Addressable Hosting Service
/ServerData/

# AssetManager Built-In Bundles
/[Aa]ssets/[Ss]treamingAssets/bb.meta
/[Aa]ssets/[Ss]treamingAssets/bb/*

# Editor Setting for PlayFab Title
TitleSettings.json

# ProjectCP Operation Console
!/OperationConsole/*
/OperationConsole/data/*
/OperationConsole/export/*

# Migrate
Assets/Migrate/MapModules/
Assets/Migrate/MapModules.meta
