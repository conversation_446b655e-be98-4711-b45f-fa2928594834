%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45f4116f2cdfba14b8cf00f26426a3d4, type: 3}
  m_Name: Ability_3000103
  m_EditorClassIdentifier: 
  blackboard:
  - key: rangeX
    value: 0
  - key: rangeY
    value: 0
  - key: lifetime
    value: 0
  - key: stun
    value: 0
  - key: energyRecovery
    value: 0
  - key: rotation
    value: 0
  - key: offsetY
    value: 0
  - key: offsetY0
    value: 0
  - key: speed
    value: 0
  - key: distanceX
    value: 0
  - key: speed0
    value: 0
  - key: distanceX0
    value: 0
  - key: speed1
    value: 0
  graphGroups:
  - graphs:
    - nodes:
      - rid: 8923418969171559187
      - rid: 8923418969171559188
      - rid: 8923418969171559189
      - rid: 8923418969171559190
      - rid: 8923418969171559191
      - rid: 8923418969171559192
      - rid: 8923418969171559193
      - rid: 8923418969171559194
      - rid: 8923418969171559195
      - rid: 8923418969171559196
      - rid: 8923418969171559197
      - rid: 8923418969171559198
      - rid: 8923418969171559199
      - rid: 8923418969171559200
      - rid: 8923418969171559201
      - rid: 8923418969171559202
      - rid: 8923418969171559203
      - rid: 8923418969171559204
      - rid: 8923418969171559205
      - rid: 8923418969171559206
      - rid: 8923418969171559207
      - rid: 8923418969171559208
      - rid: 8923418969171559209
      edges:
      - id1: 45608
        port1: next
        id2: 172975
        port2: previous
      - id1: 143083
        port1: next
        id2: 831346
        port2: previous
      - id1: 321976
        port1: character
        id2: 143083
        port2: targets
      - id1: 831346
        port1: next
        id2: 10401
        port2: previous
      - id1: 951770
        port1: character
        id2: 831346
        port2: targets
      - id1: 10401
        port1: next
        id2: 767253
        port2: previous
      - id1: 23314
        port1: character
        id2: 10401
        port2: instigator
      - id1: 571639
        port1: character
        id2: 767253
        port2: targets
      - id1: 571639
        port1: character
        id2: 406735
        port2: target
      - id1: 767253
        port1: next
        id2: 547214
        port2: previous
      - id1: 547214
        port1: next
        id2: 488138
        port2: previous
      - id1: 406735
        port1: amount
        id2: 547214
        port2: text
      - id1: 275503
        port1: value
        id2: 172975
        port2: size1
      - id1: 612421
        port1: value
        id2: 172975
        port2: speed
      - id1: 215651
        port1: value
        id2: 172975
        port2: stunTicks
      - id1: 669229
        port1: value
        id2: 172975
        port2: lifetime
      - id1: 97124
        port1: value
        id2: 172975
        port2: size2
      - id1: 231216
        port1: character
        id2: 172975
        port2: caster
      - id1: 172975
        port1: next
        id2: 143083
        port2: previous
      - id1: 290385
        port1: value
        id2: 172975
        port2: offsetY
      - id1: 810791
        port1: value
        id2: 172975
        port2: energyGainForHit
      - id1: 351433
        port1: value
        id2: 172975
        port2: directionInDegree
  - graphs:
    - nodes:
      - rid: 8923418969171559210
      - rid: 8923418969171559211
      - rid: 8923418969171559212
      - rid: 8923418969171559213
      - rid: 8923418969171559214
      edges:
      - id1: 78113
        port1: next
        id2: 468325
        port2: previous
      - id1: 78113
        port1: instigator
        id2: 468325
        port2: instigator
      - id1: 78113
        port1: target
        id2: 468325
        port2: targets
      - id1: 321401
        port1: next
        id2: 390379
        port2: previous
      - id1: 321401
        port1: caster
        id2: 390379
        port2: instigator
    - nodes:
      - rid: 8923418969171559215
      - rid: 8923418969171559216
      - rid: 8923418969171559217
      - rid: 8923418969171559218
      - rid: 8923418969171559219
      - rid: 8923418969171559220
      - rid: 8923418969171559221
      - rid: 8923418969171559222
      - rid: 8923418969171559223
      - rid: 8923418969171559224
      - rid: 8923418969171559225
      - rid: 8923418969171559226
      - rid: 8923418969171559227
      - rid: 8923418969171559228
      - rid: 8923418969171559229
      - rid: 8923418969171559230
      - rid: 8923418969171559231
      - rid: 8923418969171559232
      - rid: 8923418969171559233
      - rid: 8923418969171559234
      - rid: 8923418969171559235
      - rid: 8923418969171559236
      - rid: 8923418969171559237
      - rid: 8923418969171559238
      - rid: 8923418969171559239
      - rid: 8923418969171559240
      - rid: 8923418969171559241
      - rid: 8923418969171559242
      - rid: 8923418969171559243
      - rid: 8923418969171559244
      - rid: 8923418969171559245
      - rid: 8923418969171559246
      - rid: 8923418969171559247
      - rid: 8923418969171559248
      - rid: 8923418969171559249
      - rid: 8923418969171559250
      - rid: 8923418969171559251
      - rid: 8923418969171559252
      - rid: 8923418969171559253
      - rid: 8923418969171559254
      - rid: 8923418969171559255
      - rid: 8923418969171559256
      - rid: 8923418969171559257
      - rid: 8923418969171559258
      - rid: 8923418969171559259
      - rid: 8923418969171559260
      - rid: 8923418969171559261
      - rid: 8923418969171559262
      - rid: 8923418969171559263
      - rid: 8923418969171559264
      - rid: 8923418969171559265
      - rid: 8923418969171559266
      - rid: 8923418969171559267
      - rid: 8923418969171559268
      - rid: 8923418969171559269
      - rid: 8923418969171559270
      - rid: 8923418969171559271
      - rid: 8923418969171559272
      - rid: 8923418969171559273
      - rid: 8923418969171559274
      - rid: 8923418969171559275
      - rid: 8923418969171559276
      - rid: 8923418969171559277
      - rid: 8923418969171559278
      - rid: 8923418969171559279
      - rid: 8923418969171559280
      - rid: 8923418969171559281
      - rid: 8923418969171559282
      - rid: 8923418969171559283
      - rid: 8923418969171559284
      - rid: 8923418969171559285
      - rid: 8923418969171559286
      - rid: 8923418969171559287
      - rid: 8923418969171559288
      - rid: 8923418969171559289
      - rid: 8923418969171559290
      - rid: 8923418969171559291
      - rid: 8923418969171559292
      edges:
      - id1: 483943
        port1: next
        id2: 653660
        port2: previousPort
      - id1: 483943
        port1: caster
        id2: 197830
        port2: source
      - id1: 276503
        port1: unitId
        id2: 129628
        port2: value
      - id1: 276503
        port1: unitId
        id2: 256131
        port2: b
      - id1: 128041
        port1: next
        id2: 395120
        port2: previousPort
      - id1: 128041
        port1: entity
        id2: 629322
        port2: target
      - id1: 128041
        port1: entity
        id2: 508307
        port2: target
      - id1: 173884
        port1: result
        id2: 101027
        port2: value
      - id1: 17616
        port1: result
        id2: 921000
        port2: conditionPort
      - id1: 629322
        port1: result
        id2: 173884
        port2: a
      - id1: 508307
        port1: result
        id2: 173884
        port2: b
      - id1: 454863
        port1: next
        id2: 13031
        port2: previous
      - id1: 101027
        port1: y
        id2: 17616
        port2: a
      - id1: 640506
        port1: x
        id2: 104461
        port2: a
      - id1: 640506
        port1: x
        id2: 266230
        port2: a
      - id1: 640506
        port1: y
        id2: 454863
        port2: pathArg3
      - id1: 266230
        port1: result
        id2: 454863
        port2: pathArg2
      - id1: 395120
        port1: truePort
        id2: 921000
        port2: previousPort
      - id1: 788413
        port1: value
        id2: 44525
        port2: a
      - id1: 44525
        port1: result
        id2: 395120
        port2: conditionPort
      - id1: 921000
        port1: truePort
        id2: 580244
        port2: previous
      - id1: 769013
        port1: next
        id2: 364830
        port2: previousPort
      - id1: 769013
        port1: entity
        id2: 454863
        port2: entity
      - id1: 769013
        port1: entity
        id2: 13031
        port2: entity
      - id1: 769013
        port1: entity
        id2: 98120
        port2: target
      - id1: 364830
        port1: truePort
        id2: 454863
        port2: previous
      - id1: 420739
        port1: result
        id2: 364830
        port2: conditionPort
      - id1: 855937
        port1: value
        id2: 420739
        port2: a
      - id1: 98120
        port1: result
        id2: 640506
        port2: value
      - id1: 244623
        port1: truePort
        id2: 523295
        port2: previous
      - id1: 849271
        port1: value
        id2: 121579
        port2: unitId
      - id1: 121579
        port1: result
        id2: 467605
        port2: target
      - id1: 467605
        port1: center
        id2: 912811
        port2: value
      - id1: 700043
        port1: result
        id2: 602305
        port2: text
      - id1: 8142
        port1: result
        id2: 244623
        port2: conditionPort
      - id1: 212112
        port1: next
        id2: 1797
        port2: previousPort
      - id1: 212112
        port1: entity
        id2: 683728
        port2: entity
      - id1: 1797
        port1: truePort
        id2: 683728
        port2: previous
      - id1: 651229
        port1: result
        id2: 1797
        port2: conditionPort
      - id1: 320258
        port1: value
        id2: 651229
        port2: a
      - id1: 574908
        port1: value
        id2: 313558
        port2: unitId
      - id1: 313558
        port1: result
        id2: 68303
        port2: target
      - id1: 313558
        port1: result
        id2: 693736
        port2: targets
      - id1: 313558
        port1: result
        id2: 630836
        port2: targets
      - id1: 68303
        port1: center
        id2: 738798
        port2: value
      - id1: 738798
        port1: x
        id2: 683728
        port2: pathArg2
      - id1: 738798
        port1: y
        id2: 683728
        port2: pathArg3
      - id1: 369375
        port1: result
        id2: 137614
        port2: text
      - id1: 256131
        port1: result
        id2: 408102
        port2: text
      - id1: 197830
        port1: result
        id2: 933268
        port2: a
      - id1: 45205
        port1: value
        id2: 454863
        port2: pathArg1
      - id1: 500632
        port1: value
        id2: 683728
        port2: pathArg1
      - id1: 653660
        port1: truePort
        id2: 258629
        port2: previous
      - id1: 653660
        port1: falsePort
        id2: 129628
        port2: previous
      - id1: 933268
        port1: result
        id2: 653660
        port2: conditionPort
      - id1: 235664
        port1: result
        id2: 933268
        port2: b
      - id1: 929931
        port1: character
        id2: 89840
        port2: b
      - id1: 505928
        port1: result
        id2: 733224
        port2: b
      - id1: 508505
        port1: next
        id2: 949331
        port2: previous
      - id1: 733224
        port1: result
        id2: 508505
        port2: text
      - id1: 949331
        port1: next
        id2: 433502
        port2: previous
      - id1: 369049
        port1: result
        id2: 949331
        port2: text
      - id1: 89840
        port1: result
        id2: 433502
        port2: text
      - id1: 538652
        port1: value
        id2: 17616
        port2: b
      - id1: 932744
        port1: value
        id2: 8142
        port2: b
      - id1: 912811
        port1: x
        id2: 104461
        port2: b
      - id1: 258273
        port1: character
        id2: 369049
        port2: b
      - id1: 959405
        port1: character
        id2: 235664
        port2: source
      - id1: 146972
        port1: character
        id2: 276503
        port2: target
      - id1: 104461
        port1: result
        id2: 753867
        port2: source
      - id1: 753867
        port1: result
        id2: 8142
        port2: a
      - id1: 693736
        port1: next
        id2: 120299
        port2: previous
      - id1: 615564
        port1: next
        id2: 693736
        port2: previous
      - id1: 13031
        port1: next
        id2: 244623
        port2: previousPort
      - id1: 667832
        port1: next
        id2: 630836
        port2: previous
    - nodes:
      - rid: 8923418969171559293
      - rid: 8923418969171559294
      - rid: 8923418969171559295
      - rid: 8923418969171559296
      - rid: 8923418969171559297
      - rid: 8923418969171559298
      - rid: 8923418969171559299
      - rid: 8923418969171559300
      - rid: 8923418969171559301
      - rid: 8923418969171559302
      - rid: 8923418969171559303
      - rid: 8923418969171559304
      - rid: 8923418969171559305
      - rid: 8923418969171559306
      - rid: 8923418969171559307
      - rid: 8923418969171559308
      - rid: 8923418969171559309
      - rid: 8923418969171559310
      - rid: 8923418969171559311
      - rid: 8923418969171559312
      - rid: 8923418969171559313
      - rid: 8923418969171559314
      - rid: 8923418969171559315
      - rid: 8923418969171559316
      - rid: 8923418969171559317
      edges:
      - id1: 476645
        port1: next
        id2: 428731
        port2: previousPort
      - id1: 476645
        port1: entity
        id2: 844646
        port2: entity
      - id1: 476645
        port1: entity
        id2: 277022
        port2: entity
      - id1: 476645
        port1: entity
        id2: 358981
        port2: target
      - id1: 476645
        port1: entity
        id2: 728233
        port2: entity
      - id1: 428731
        port1: truePort
        id2: 812664
        port2: previousPort
      - id1: 65279
        port1: result
        id2: 428731
        port2: conditionPort
      - id1: 423991
        port1: value
        id2: 65279
        port2: a
      - id1: 812664
        port1: truePort
        id2: 844646
        port2: previous
      - id1: 812664
        port1: falsePort
        id2: 947777
        port2: previousPort
      - id1: 86658
        port1: value
        id2: 4033
        port2: unitId
      - id1: 4033
        port1: result
        id2: 764539
        port2: target
      - id1: 764539
        port1: center
        id2: 975400
        port2: value
      - id1: 358981
        port1: result
        id2: 419698
        port2: value
      - id1: 302522
        port1: result
        id2: 812664
        port2: conditionPort
      - id1: 562360
        port1: value
        id2: 302522
        port2: b
      - id1: 889845
        port1: value
        id2: 844646
        port2: speed
      - id1: 947777
        port1: truePort
        id2: 277022
        port2: previous
      - id1: 947777
        port1: falsePort
        id2: 728233
        port2: previous
      - id1: 235406
        port1: result
        id2: 947777
        port2: conditionPort
      - id1: 343138
        port1: value
        id2: 235406
        port2: b
      - id1: 169780
        port1: value
        id2: 277022
        port2: speed
      - id1: 795727
        port1: value
        id2: 728233
        port2: speed
      - id1: 809820
        port1: result
        id2: 610603
        port2: source
      - id1: 610603
        port1: result
        id2: 302522
        port2: a
      - id1: 610603
        port1: result
        id2: 235406
        port2: a
      - id1: 419698
        port1: x
        id2: 809820
        port2: a
      - id1: 975400
        port1: x
        id2: 809820
        port2: b
  - graphs:
    - nodes:
      - rid: 8923418969171559318
      - rid: 8923418969171559319
      - rid: 8923418969171559320
      - rid: 8923418969171559321
      - rid: 8923418969171559322
      edges:
      - id1: 490387
        port1: next
        id2: 614265
        port2: previous
      - id1: 490387
        port1: instigator
        id2: 614265
        port2: instigator
      - id1: 490387
        port1: target
        id2: 614265
        port2: targets
      - id1: 585998
        port1: next
        id2: 765697
        port2: previous
      - id1: 585998
        port1: caster
        id2: 765697
        port2: instigator
  references:
    version: 2
    RefIds:
    - rid: 8923418969171559187
      type: {class: FinishNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 488138
    - rid: 8923418969171559188
      type: {class: OnActivatedData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 45608
        ignoreStun: 0
    - rid: 8923418969171559189
      type: {class: AddCameraBumpByActorNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 143083
        ownerOnly: 1
        bumpId: -2
    - rid: 8923418969171559190
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 321976
    - rid: 8923418969171559191
      type: {class: AddCameraShakeByActorNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 831346
        ownerOnly: 1
        shakeId: -2
    - rid: 8923418969171559192
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 951770
    - rid: 8923418969171559193
      type: {class: AbilityLogNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 10401
        eventType: 1
        param1: 1
        param2: 0
        param3: 0
        param4: 0
    - rid: 8923418969171559194
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 23314
    - rid: 8923418969171559195
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 571639
    - rid: 8923418969171559196
      type: {class: TokenAddNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 767253
        tokenId: 0
        amount: 1000
    - rid: 8923418969171559197
      type: {class: LogNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 547214
        text: 0
    - rid: 8923418969171559198
      type: {class: GetTokenNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 406735
        tokenId: 0
    - rid: 8923418969171559199
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 275503
        key: rangeX
    - rid: 8923418969171559200
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 612421
        key: speed
    - rid: 8923418969171559201
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 215651
        key: stun
    - rid: 8923418969171559202
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 669229
        key: lifetime
    - rid: 8923418969171559203
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 97124
        key: rangeY
    - rid: 8923418969171559204
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 231216
    - rid: 8923418969171559205
      type: {class: CreateEntityNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 172975
        entityType: 6
        breakType: 4
        activationDelay: 0
        lifetime: 0
        size1: 0
        size2: 0
        rotation: 0
        offsetX: 0
        offsetY: 0
        directionInDegree: 0
        speed: 0
        faceDirection: 1
        isPenetrate: 0
        stunTicks: 0
        energyGainForHit: 0
        energyGainForBreak: 0
        hitPeriodTick: 0
        gameplayAbilityId: -1
        groupIndex: 1
        fxDataId: 600004
        hitFxDataId: -2
        isNoHit: 1
        lockX: 0
        lockY: 0
        canHitLevelTrap: 0
        canBreakLevelTrap: 0
        ignoreHitIfPreviousHit: 0
        isFRKRule: 0
        shapeType: 1
        pathType: 51
        rootType: 1
        pathArg1: 0
        pathArg2: 0
        pathArg3: 0
    - rid: 8923418969171559206
      type: {class: StringNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 856584
        text: byte, Area Trap Projectile Character, 0 1 1 0 = 6
    - rid: 8923418969171559207
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 290385
        key: offsetY
    - rid: 8923418969171559208
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 810791
        key: energyRecovery
    - rid: 8923418969171559209
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 351433
        key: rotation
    - rid: 8923418969171559210
      type: {class: OnProjectileApplyStunNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 78113
    - rid: 8923418969171559211
      type: {class: AbilityLogNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 468325
        eventType: 2
        param1: 1
        param2: 0
        param3: 0
        param4: 0
    - rid: 8923418969171559212
      type: {class: OnObjectBreakOtherData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 321401
    - rid: 8923418969171559213
      type: {class: AbilityLogNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 390379
        eventType: 11
        param1: 1
        param2: 0
        param3: 0
        param4: 0
    - rid: 8923418969171559214
      type: {class: StringNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 661177
        text: "Todo: \u5F85\u88DC\u6253\u5230\u9632\u8B77\u7F69\u7684\u60C5\u6CC1"
    - rid: 8923418969171559215
      type: {class: OnObjectStartNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 483943
    - rid: 8923418969171559216
      type: {class: GetUnitIdNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 276503
    - rid: 8923418969171559217
      type: {class: SetParamNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 129628
        index: 0
        value: 0
    - rid: 8923418969171559218
      type: {class: StringNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 420673
        text: "Param 0\uFF1A\u9396\u5B9A\u7B2C\u4E00\u540D\u8DD1\u8005"
    - rid: 8923418969171559219
      type: {class: OnObjectTickNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 128041
    - rid: 8923418969171559220
      type: {class: GetVectorNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 173884
        a: {x: 0, y: 0}
        b: {x: 0, y: 0}
    - rid: 8923418969171559221
      type: {class: GreaterOrEqualNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 17616
        a: 0
        b: 0
    - rid: 8923418969171559222
      type: {class: GetObjectStartPositionNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 629322
    - rid: 8923418969171559223
      type: {class: GetObjectPositionNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 508307
    - rid: 8923418969171559224
      type: {class: SetProjectilePathNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 454863
        pathType: 51
        pathArg1: 0
        pathArg2: 0
        pathArg3: 0
    - rid: 8923418969171559225
      type: {class: GetVector2XYNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 101027
        value: {x: 0, y: 0}
    - rid: 8923418969171559226
      type: {class: GetVector2XYNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 640506
        value: {x: 0, y: 0}
    - rid: 8923418969171559227
      type: {class: IntAddNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 266230
        a: 0
        b: 100000
    - rid: 8923418969171559228
      type: {class: StringNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 264921
        text: "Param 1=0:\u5411\u4E0A\u98DB"
    - rid: 8923418969171559229
      type: {class: IfElseNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 395120
        conditionPort: 0
    - rid: 8923418969171559230
      type: {class: GetParamNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 788413
        index: 1
    - rid: 8923418969171559231
      type: {class: EqualNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 44525
        a: 0
        b: 0
    - rid: 8923418969171559232
      type: {class: IfElseNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 921000
        conditionPort: 0
    - rid: 8923418969171559233
      type: {class: SetParamNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 580244
        index: 1
        value: 1
    - rid: 8923418969171559234
      type: {class: OnObjectTickNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 769013
    - rid: 8923418969171559235
      type: {class: IfElseNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 364830
        conditionPort: 0
    - rid: 8923418969171559236
      type: {class: EqualNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 420739
        a: 0
        b: 1
    - rid: 8923418969171559237
      type: {class: GetParamNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 855937
        index: 1
    - rid: 8923418969171559238
      type: {class: GetObjectPositionNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 98120
    - rid: 8923418969171559239
      type: {class: LogNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 36887
        text: "\u968E\u6BB5\u4E00"
    - rid: 8923418969171559240
      type: {class: LogNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 602305
        text: 
    - rid: 8923418969171559241
      type: {class: IfElseNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 244623
        conditionPort: 0
    - rid: 8923418969171559242
      type: {class: GetParamNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 849271
        index: 0
    - rid: 8923418969171559243
      type: {class: GetUnitByIdNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 121579
        unitId: 0
    - rid: 8923418969171559244
      type: {class: GetCharacterCenterNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 467605
    - rid: 8923418969171559245
      type: {class: ConcatNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 700043
        a: "\u968E\u6BB5\u4E8C"
        b: 
    - rid: 8923418969171559246
      type: {class: LessNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 8142
        a: 0
        b: 0
    - rid: 8923418969171559247
      type: {class: SetParamNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 523295
        index: 1
        value: 2
    - rid: 8923418969171559248
      type: {class: OnObjectTickNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 212112
    - rid: 8923418969171559249
      type: {class: IfElseNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 1797
        conditionPort: 0
    - rid: 8923418969171559250
      type: {class: EqualNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 651229
        a: 0
        b: 2
    - rid: 8923418969171559251
      type: {class: GetParamNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 320258
        index: 1
    - rid: 8923418969171559252
      type: {class: SetProjectilePathNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 683728
        pathType: 51
        pathArg1: 0
        pathArg2: 0
        pathArg3: 0
    - rid: 8923418969171559253
      type: {class: GetParamNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 574908
        index: 0
    - rid: 8923418969171559254
      type: {class: GetUnitByIdNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 313558
        unitId: 0
    - rid: 8923418969171559255
      type: {class: GetCharacterCenterNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 68303
    - rid: 8923418969171559256
      type: {class: LogNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 137614
        text: 
    - rid: 8923418969171559257
      type: {class: GetVector2XYNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 738798
        value: {x: 0, y: 0}
    - rid: 8923418969171559258
      type: {class: ConcatNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 369375
        a: "\u968E\u6BB5\u4E09"
        b: 
    - rid: 8923418969171559259
      type: {class: LogNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 408102
        text: 
    - rid: 8923418969171559260
      type: {class: ConcatNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 256131
        a: "\u968E\u6BB5\u96F6"
        b: 0
    - rid: 8923418969171559261
      type: {class: GetTeamIndexNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 197830
    - rid: 8923418969171559262
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 45205
        key: rotation
    - rid: 8923418969171559263
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 500632
        key: rotation
    - rid: 8923418969171559264
      type: {class: StringNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 968675
        text: "\u7B2C\u4E00\u540D\u662F\u5426\u70BA\u540C\u968A"
    - rid: 8923418969171559265
      type: {class: IfElseNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 653660
        conditionPort: 0
    - rid: 8923418969171559266
      type: {class: EqualNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 933268
        a: 0
        b: 0
    - rid: 8923418969171559267
      type: {class: GetTeamIndexNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 235664
    - rid: 8923418969171559268
      type: {class: SetParamNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 258629
        index: 1
        value: 99
    - rid: 8923418969171559269
      type: {class: StringNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 594934
        text: "Param 1=1:\u5411\u53F3\u98DB"
    - rid: 8923418969171559270
      type: {class: StringNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 165966
        text: "Param 1=2:\u8FFD\u8E64\u98DB"
    - rid: 8923418969171559271
      type: {class: GetCharacterByRankNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 929931
        rank: 1
        includeNotRunning: 0
    - rid: 8923418969171559272
      type: {class: GetTeamIndexNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 505928
    - rid: 8923418969171559273
      type: {class: LogNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 508505
        text: 
    - rid: 8923418969171559274
      type: {class: ConcatNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 733224
        a: "\u81EA\u8EAB\u968A\u4F0D"
        b: 0
    - rid: 8923418969171559275
      type: {class: LogNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 949331
        text: 
    - rid: 8923418969171559276
      type: {class: ConcatNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 369049
        a: "FrontRunner\u968A\u4F0D"
        b: 
    - rid: 8923418969171559277
      type: {class: LogNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 433502
        text: 
    - rid: 8923418969171559278
      type: {class: ConcatNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 89840
        a: "Rank1\u968A\u4F0D"
        b: 
    - rid: 8923418969171559279
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 538652
        key: offsetY0
    - rid: 8923418969171559280
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 932744
        key: distanceX0
    - rid: 8923418969171559281
      type: {class: GetVector2XYNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 912811
        value: {x: 0, y: 0}
    - rid: 8923418969171559282
      type: {class: GetCharacterByRankNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 258273
        rank: 1
        includeNotRunning: 0
    - rid: 8923418969171559283
      type: {class: GetCharacterByRankNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 959405
        rank: 1
        includeNotRunning: 0
    - rid: 8923418969171559284
      type: {class: GetCharacterByRankNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 146972
        rank: 1
        includeNotRunning: 0
    - rid: 8923418969171559285
      type: {class: IntSubtractNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 104461
        a: 0
        b: 0
    - rid: 8923418969171559286
      type: {class: AbsNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 753867
        source: 0
    - rid: 8923418969171559287
      type: {class: AppendVfxNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 693736
        ownerOnly: 1
        targetsOnly: 0
        vfxName: Vfx_006_Diana_00_Passive_Target
        sfxName: 
        boneType: 1
        customBoneId: 0
        offsetX: 0
        offsetY: 0
    - rid: 8923418969171559288
      type: {class: OnObjectTickNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 615564
    - rid: 8923418969171559289
      type: {class: LogNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 120299
        text: "\u5237\u7279\u6548"
    - rid: 8923418969171559290
      type: {class: SetProjectileNoHitNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 13031
        noHit: 0
    - rid: 8923418969171559291
      type: {class: OnTickNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 667832
        ignoreStun: 1
        ignoreKnockedDown: 1
    - rid: 8923418969171559292
      type: {class: AppendVfxNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 630836
        ownerOnly: 0
        targetsOnly: 1
        vfxName: Vfx_006_Diana_00_Passive_Targeted
        sfxName: 
        boneType: 1
        customBoneId: 0
        offsetX: 0
        offsetY: 0
    - rid: 8923418969171559293
      type: {class: SetProjectileSpeedNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 844646
        speed: 0
    - rid: 8923418969171559294
      type: {class: OnObjectTickNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 476645
    - rid: 8923418969171559295
      type: {class: IfElseNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 428731
        conditionPort: 0
    - rid: 8923418969171559296
      type: {class: GreaterOrEqualNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 65279
        a: 0
        b: 1
    - rid: 8923418969171559297
      type: {class: GetParamNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 423991
        index: 1
    - rid: 8923418969171559298
      type: {class: IfElseNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 812664
        conditionPort: 0
    - rid: 8923418969171559299
      type: {class: GetParamNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 86658
        index: 0
    - rid: 8923418969171559300
      type: {class: GetUnitByIdNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 4033
        unitId: 0
    - rid: 8923418969171559301
      type: {class: GetCharacterCenterNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 764539
    - rid: 8923418969171559302
      type: {class: GetObjectPositionNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 358981
    - rid: 8923418969171559303
      type: {class: LessOrEqualNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 302522
        a: 0
        b: 0
    - rid: 8923418969171559304
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 562360
        key: distanceX0
    - rid: 8923418969171559305
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 889845
        key: speed1
    - rid: 8923418969171559306
      type: {class: IfElseNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 947777
        conditionPort: 0
    - rid: 8923418969171559307
      type: {class: LessOrEqualNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 235406
        a: 0
        b: 0
    - rid: 8923418969171559308
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 343138
        key: distanceX
    - rid: 8923418969171559309
      type: {class: SetProjectileSpeedNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 277022
        speed: 0
    - rid: 8923418969171559310
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 169780
        key: speed0
    - rid: 8923418969171559311
      type: {class: SetProjectileSpeedNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 728233
        speed: 0
    - rid: 8923418969171559312
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 795727
        key: speed
    - rid: 8923418969171559313
      type: {class: StringNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 914585
        text: "\u4F9D\u8DDD\u96E2\u8ABF\u6574\u901F\u5EA6"
    - rid: 8923418969171559314
      type: {class: IntSubtractNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 809820
        a: 0
        b: 0
    - rid: 8923418969171559315
      type: {class: AbsNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 610603
        source: 0
    - rid: 8923418969171559316
      type: {class: GetVector2XYNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 419698
        value: {x: 0, y: 0}
    - rid: 8923418969171559317
      type: {class: GetVector2XYNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 975400
        value: {x: 0, y: 0}
    - rid: 8923418969171559318
      type: {class: OnProjectileApplyStunNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 490387
    - rid: 8923418969171559319
      type: {class: AbilityLogNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 614265
        eventType: 2
        param1: 0
        param2: 1
        param3: 0
        param4: 0
    - rid: 8923418969171559320
      type: {class: OnObjectBreakOtherData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 585998
    - rid: 8923418969171559321
      type: {class: AbilityLogNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 765697
        eventType: 11
        param1: 0
        param2: 1
        param3: 0
        param4: 0
    - rid: 8923418969171559322
      type: {class: StringNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 418354
        text: "Todo: \u5F85\u88DC\u6253\u5230\u9632\u8B77\u7F69\u7684\u60C5\u6CC1"
