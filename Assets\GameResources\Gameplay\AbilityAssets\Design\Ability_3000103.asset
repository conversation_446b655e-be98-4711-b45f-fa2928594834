%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 32f4e526f6c4d2a49979457203108d7a, type: 3}
  m_Name: Ability_3000103
  m_EditorClassIdentifier: 
  blackboard:
  - key: rangeX
    value: 0
  - key: rangeY
    value: 0
  - key: lifetime
    value: 0
  - key: stun
    value: 0
  - key: energyRecovery
    value: 0
  - key: rotation
    value: 0
  - key: offsetY
    value: 0
  - key: offsetY0
    value: 0
  - key: speed
    value: 0
  - key: distanceX
    value: 0
  - key: speed0
    value: 0
  - key: distanceX0
    value: 0
  - key: speed1
    value: 0
  graphJsons: []
  graphGroups:
  - graphs:
    - '{"_type":"Physalia.Flexi.AbilityGraph","nodes":[{"_id":488138,"_position":{"x":3005.0,"y":245.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.FinishNode"},{"_id":45608,"_position":{"x":-15.0,"y":186.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.OnActivated"},{"_id":143083,"_position":{"x":1408.0,"y":186.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.AddCameraBumpByActorNode","ownerOnly":true,"bumpId":-2},{"_id":321976,"_position":{"x":1256.0,"y":366.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":831346,"_position":{"x":1721.0,"y":186.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.AddCameraShakeByActorNode","ownerOnly":true,"shakeId":-2},{"_id":951770,"_position":{"x":1559.0,"y":366.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":10401,"_position":{"x":2063.0,"y":236.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.AbilityLogNode","eventType":1,"param1":1},{"_id":23314,"_position":{"x":1864.0,"y":366.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":571639,"_position":{"x":2234.0,"y":154.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":767253,"_position":{"x":2458.0,"y":223.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.TokenAddNode","amount":1000},{"_id":547214,"_position":{"x":2765.0,"y":224.0},"_type":"Physalia.Flexi.LogNode"},{"_id":406735,"_position":{"x":2500.0,"y":438.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.GetTokenNode"},{"_id":275503,"_position":{"x":388.0,"y":522.0},"_type":"Physalia.Flexi.BlackboardNode","key":"rangeX"},{"_id":612421,"_position":{"x":388.0,"y":718.0},"_type":"Physalia.Flexi.BlackboardNode","key":"speed"},{"_id":215651,"_position":{"x":388.0,"y":816.0},"_type":"Physalia.Flexi.BlackboardNode","key":"stun"},{"_id":669229,"_position":{"x":389.0,"y":424.0},"_type":"Physalia.Flexi.BlackboardNode","key":"lifetime"},{"_id":97124,"_position":{"x":388.0,"y":620.0},"_type":"Physalia.Flexi.BlackboardNode","key":"rangeY"},{"_id":231216,"_position":{"x":410.0,"y":347.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":172975,"_position":{"x":776.0,"y":183.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.CreateEntityNode","entityType":6,"breakType":4,"faceDirection":true,"gameplayAbilityId":-1,"groupIndex":1,"fxDataId":600004,"hitFxDataId":-2,"isNoHit":true,"lockX":false,"lockY":false,"canHitLevelTrap":false,"canBreakLevelTrap":false,"ignoreHitIfPreviousHit":false,"isFRKRule":false,"shapeType":1,"pathType":51,"rootType":1,"pathArg1":0,"pathArg2":0,"pathArg3":0},{"_id":856584,"_position":{"x":675.0,"y":1090.0},"_type":"Physalia.Flexi.StringNode","text":"byte,
      Area Trap Projectile Character, 0 1 1 0 = 6"},{"_id":290385,"_position":{"x":248.0,"y":489.0},"_type":"Physalia.Flexi.BlackboardNode","key":"offsetY"},{"_id":810791,"_position":{"x":322.0,"y":965.0},"_type":"Physalia.Flexi.BlackboardNode","key":"energyRecovery"},{"_id":351433,"_position":{"x":239.0,"y":621.0},"_type":"Physalia.Flexi.BlackboardNode","key":"rotation"}],"edges":[{"id1":547214,"port1":"next","id2":488138,"port2":"previous"},{"id1":767253,"port1":"next","id2":547214,"port2":"previous"},{"id1":406735,"port1":"amount","id2":547214,"port2":"text"},{"id1":10401,"port1":"next","id2":767253,"port2":"previous"},{"id1":571639,"port1":"character","id2":767253,"port2":"targets"},{"id1":831346,"port1":"next","id2":10401,"port2":"previous"},{"id1":23314,"port1":"character","id2":10401,"port2":"instigator"},{"id1":143083,"port1":"next","id2":831346,"port2":"previous"},{"id1":951770,"port1":"character","id2":831346,"port2":"targets"},{"id1":172975,"port1":"next","id2":143083,"port2":"previous"},{"id1":321976,"port1":"character","id2":143083,"port2":"targets"},{"id1":45608,"port1":"next","id2":172975,"port2":"previous"},{"id1":231216,"port1":"character","id2":172975,"port2":"caster"},{"id1":669229,"port1":"value","id2":172975,"port2":"lifetime"},{"id1":275503,"port1":"value","id2":172975,"port2":"size1"},{"id1":97124,"port1":"value","id2":172975,"port2":"size2"},{"id1":290385,"port1":"value","id2":172975,"port2":"offsetY"},{"id1":351433,"port1":"value","id2":172975,"port2":"directionInDegree"},{"id1":612421,"port1":"value","id2":172975,"port2":"speed"},{"id1":215651,"port1":"value","id2":172975,"port2":"stunTicks"},{"id1":810791,"port1":"value","id2":172975,"port2":"energyGainForHit"},{"id1":571639,"port1":"character","id2":406735,"port2":"target"}]}'
  - graphs:
    - "{\"_type\":\"Physalia.Flexi.AbilityGraph\",\"nodes\":[{\"_id\":78113,\"_position\":{\"x\":180.0,\"y\":215.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.OnProjectileApplyStunNode\"},{\"_id\":468325,\"_position\":{\"x\":563.0,\"y\":215.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.AbilityLogNode\",\"eventType\":2,\"param1\":1},{\"_id\":321401,\"_position\":{\"x\":191.0,\"y\":556.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.OnObjectBreakOther\"},{\"_id\":390379,\"_position\":{\"x\":563.0,\"y\":556.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.AbilityLogNode\",\"eventType\":11,\"param1\":1},{\"_id\":661177,\"_position\":{\"x\":180.0,\"y\":92.0},\"_type\":\"Physalia.Flexi.StringNode\",\"text\":\"Todo:
      \u5F85\u88DC\u6253\u5230\u9632\u8B77\u7F69\u7684\u60C5\u6CC1\"}],\"edges\":[{\"id1\":78113,\"port1\":\"next\",\"id2\":468325,\"port2\":\"previous\"},{\"id1\":78113,\"port1\":\"instigator\",\"id2\":468325,\"port2\":\"instigator\"},{\"id1\":78113,\"port1\":\"target\",\"id2\":468325,\"port2\":\"targets\"},{\"id1\":321401,\"port1\":\"next\",\"id2\":390379,\"port2\":\"previous\"},{\"id1\":321401,\"port1\":\"caster\",\"id2\":390379,\"port2\":\"instigator\"}]}"
    - "{\"_type\":\"Physalia.Flexi.AbilityGraph\",\"nodes\":[{\"_id\":483943,\"_position\":{\"x\":175.0,\"y\":191.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.OnObjectStartNode\"},{\"_id\":276503,\"_position\":{\"x\":1318.0,\"y\":453.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetUnitIdNode\"},{\"_id\":129628,\"_position\":{\"x\":1485.0,\"y\":223.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SetParamNode\"},{\"_id\":420673,\"_position\":{\"x\":1572.0,\"y\":76.0},\"_type\":\"Physalia.Flexi.StringNode\",\"text\":\"Param
      0\uFF1A\u9396\u5B9A\u7B2C\u4E00\u540D\u8DD1\u8005\"},{\"_id\":128041,\"_position\":{\"x\":176.0,\"y\":772.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.OnObjectTickNode\"},{\"_id\":173884,\"_position\":{\"x\":746.0,\"y\":1125.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetVectorNode\"},{\"_id\":17616,\"_position\":{\"x\":963.0,\"y\":896.0},\"_type\":\"Physalia.Flexi.GreaterOrEqualNode\",\"b\":30000},{\"_id\":629322,\"_position\":{\"x\":508.0,\"y\":994.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetObjectStartPositionNode\"},{\"_id\":508307,\"_position\":{\"x\":505.0,\"y\":1102.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetObjectPositionNode\"},{\"_id\":454863,\"_position\":{\"x\":954.0,\"y\":1427.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SetProjectilePathNode\",\"pathType\":51,\"pathArg1\":900000},{\"_id\":101027,\"_position\":{\"x\":763.0,\"y\":957.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetVector2XYNode\"},{\"_id\":640506,\"_position\":{\"x\":405.0,\"y\":1759.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetVector2XYNode\"},{\"_id\":266230,\"_position\":{\"x\":602.0,\"y\":1583.0},\"_type\":\"Physalia.Flexi.IntAddNode\",\"b\":100000},{\"_id\":264921,\"_position\":{\"x\":176.0,\"y\":662.0},\"_type\":\"Physalia.Flexi.StringNode\",\"text\":\"Param
      1=0:\u5411\u4E0A\u98DB\"},{\"_id\":395120,\"_position\":{\"x\":526.0,\"y\":774.0},\"_type\":\"Physalia.Flexi.IfElseNode\"},{\"_id\":788413,\"_position\":{\"x\":-37.0,\"y\":1024.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetParamNode\",\"index\":1},{\"_id\":44525,\"_position\":{\"x\":167.0,\"y\":1026.0},\"_type\":\"Physalia.Flexi.EqualNode\"},{\"_id\":921000,\"_position\":{\"x\":1122.0,\"y\":777.0},\"_type\":\"Physalia.Flexi.IfElseNode\"},{\"_id\":580244,\"_position\":{\"x\":1397.0,\"y\":777.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SetParamNode\",\"index\":1,\"value\":1},{\"_id\":769013,\"_position\":{\"x\":183.0,\"y\":1428.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.OnObjectTickNode\"},{\"_id\":364830,\"_position\":{\"x\":515.0,\"y\":1427.0},\"_type\":\"Physalia.Flexi.IfElseNode\"},{\"_id\":420739,\"_position\":{\"x\":259.0,\"y\":1606.0},\"_type\":\"Physalia.Flexi.EqualNode\",\"b\":1},{\"_id\":855937,\"_position\":{\"x\":57.0,\"y\":1607.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetParamNode\",\"index\":1},{\"_id\":98120,\"_position\":{\"x\":95.0,\"y\":1758.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetObjectPositionNode\"},{\"_id\":36887,\"_position\":{\"x\":1693.0,\"y\":777.0},\"_type\":\"Physalia.Flexi.LogNode\",\"text\":\"\u968E\u6BB5\u4E00\"},{\"_id\":602305,\"_position\":{\"x\":2552.0,\"y\":1647.0},\"_type\":\"Physalia.Flexi.LogNode\",\"text\":\"\u968E\u6BB5\u4E8C\"},{\"_id\":244623,\"_position\":{\"x\":1778.0,\"y\":1428.0},\"_type\":\"Physalia.Flexi.IfElseNode\"},{\"_id\":849271,\"_position\":{\"x\":593.0,\"y\":1887.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetParamNode\"},{\"_id\":121579,\"_position\":{\"x\":766.0,\"y\":1887.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetUnitByIdNode\"},{\"_id\":467605,\"_position\":{\"x\":944.0,\"y\":1887.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetCharacterCenterNode\"},{\"_id\":700043,\"_position\":{\"x\":2203.0,\"y\":1782.0},\"_type\":\"Physalia.Flexi.ConcatNode\",\"a\":\"\u968E\u6BB5\u4E8C\"},{\"_id\":8142,\"_position\":{\"x\":1712.0,\"y\":1605.0},\"_type\":\"Physalia.Flexi.LessNode\"},{\"_id\":523295,\"_position\":{\"x\":2084.0,\"y\":1427.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SetParamNode\",\"index\":1,\"value\":2},{\"_id\":212112,\"_position\":{\"x\":181.0,\"y\":2061.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.OnObjectTickNode\"},{\"_id\":1797,\"_position\":{\"x\":498.0,\"y\":2064.0},\"_type\":\"Physalia.Flexi.IfElseNode\"},{\"_id\":651229,\"_position\":{\"x\":287.0,\"y\":2285.0},\"_type\":\"Physalia.Flexi.EqualNode\",\"b\":2},{\"_id\":320258,\"_position\":{\"x\":85.0,\"y\":2286.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetParamNode\",\"index\":1},{\"_id\":683728,\"_position\":{\"x\":943.0,\"y\":2056.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SetProjectilePathNode\",\"pathType\":51,\"pathArg1\":900000},{\"_id\":574908,\"_position\":{\"x\":83.0,\"y\":2582.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetParamNode\"},{\"_id\":313558,\"_position\":{\"x\":256.0,\"y\":2582.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetUnitByIdNode\"},{\"_id\":68303,\"_position\":{\"x\":434.0,\"y\":2582.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetCharacterCenterNode\"},{\"_id\":137614,\"_position\":{\"x\":1355.0,\"y\":2057.0},\"_type\":\"Physalia.Flexi.LogNode\",\"text\":\"\u968E\u6BB5\u4E09\"},{\"_id\":738798,\"_position\":{\"x\":707.0,\"y\":2349.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetVector2XYNode\"},{\"_id\":369375,\"_position\":{\"x\":1211.0,\"y\":2203.0},\"_type\":\"Physalia.Flexi.ConcatNode\",\"a\":\"\u968E\u6BB5\u4E09\"},{\"_id\":408102,\"_position\":{\"x\":1828.0,\"y\":285.0},\"_type\":\"Physalia.Flexi.LogNode\",\"text\":\"\u968E\u6BB5\u96F6\"},{\"_id\":256131,\"_position\":{\"x\":1625.0,\"y\":430.0},\"_type\":\"Physalia.Flexi.ConcatNode\",\"a\":\"\u968E\u6BB5\u96F6\"},{\"_id\":197830,\"_position\":{\"x\":375.0,\"y\":318.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetTeamIndexNode\"},{\"_id\":45205,\"_position\":{\"x\":856.0,\"y\":1669.0},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"rotation\"},{\"_id\":500632,\"_position\":{\"x\":706.0,\"y\":2213.0},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"rotation\"},{\"_id\":968675,\"_position\":{\"x\":815.0,\"y\":77.0},\"_type\":\"Physalia.Flexi.StringNode\",\"text\":\"\u7B2C\u4E00\u540D\u662F\u5426\u70BA\u540C\u968A\"},{\"_id\":653660,\"_position\":{\"x\":812.0,\"y\":193.0},\"_type\":\"Physalia.Flexi.IfElseNode\"},{\"_id\":933268,\"_position\":{\"x\":652.0,\"y\":343.0},\"_type\":\"Physalia.Flexi.EqualNode\"},{\"_id\":235664,\"_position\":{\"x\":377.0,\"y\":417.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetTeamIndexNode\"},{\"_id\":258629,\"_position\":{\"x\":1141.0,\"y\":198.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SetParamNode\",\"index\":1,\"value\":99},{\"_id\":594934,\"_position\":{\"x\":181.0,\"y\":1301.0},\"_type\":\"Physalia.Flexi.StringNode\",\"text\":\"Param
      1=1:\u5411\u53F3\u98DB\"},{\"_id\":165966,\"_position\":{\"x\":185.0,\"y\":1932.0},\"_type\":\"Physalia.Flexi.StringNode\",\"text\":\"Param
      1=2:\u8FFD\u8E64\u98DB\"},{\"_id\":929931,\"_position\":{\"x\":1120.0,\"y\":-23.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetCharacterByRankNode\",\"rank\":1},{\"_id\":505928,\"_position\":{\"x\":383.0,\"y\":-121.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetTeamIndexNode\"},{\"_id\":508505,\"_position\":{\"x\":1003.0,\"y\":-239.0},\"_type\":\"Physalia.Flexi.LogNode\"},{\"_id\":733224,\"_position\":{\"x\":656.0,\"y\":-127.0},\"_type\":\"Physalia.Flexi.ConcatNode\",\"a\":\"\u81EA\u8EAB\u968A\u4F0D\"},{\"_id\":949331,\"_position\":{\"x\":1288.0,\"y\":-237.0},\"_type\":\"Physalia.Flexi.LogNode\"},{\"_id\":369049,\"_position\":{\"x\":671.0,\"y\":-7.0},\"_type\":\"Physalia.Flexi.ConcatNode\",\"a\":\"FrontRunner\u968A\u4F0D\"},{\"_id\":433502,\"_position\":{\"x\":1594.0,\"y\":-237.0},\"_type\":\"Physalia.Flexi.LogNode\"},{\"_id\":89840,\"_position\":{\"x\":1710.0,\"y\":-79.0},\"_type\":\"Physalia.Flexi.ConcatNode\",\"a\":\"Rank1\u968A\u4F0D\"},{\"_id\":538652,\"_position\":{\"x\":1047.0,\"y\":1104.0},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"offsetY0\"},{\"_id\":932744,\"_position\":{\"x\":1412.0,\"y\":1592.0},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"distanceX0\"},{\"_id\":912811,\"_position\":{\"x\":1182.0,\"y\":1887.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetVector2XYNode\"},{\"_id\":258273,\"_position\":{\"x\":283.0,\"y\":24.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetCharacterByRankNode\",\"rank\":1},{\"_id\":959405,\"_position\":{\"x\":-49.0,\"y\":477.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetCharacterByRankNode\",\"rank\":1},{\"_id\":146972,\"_position\":{\"x\":841.0,\"y\":500.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetCharacterByRankNode\",\"rank\":1},{\"_id\":104461,\"_position\":{\"x\":1430.0,\"y\":1769.0},\"_type\":\"Physalia.Flexi.IntSubtractNode\"},{\"_id\":753867,\"_position\":{\"x\":1642.0,\"y\":1770.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.AbsNode\"},{\"_id\":693736,\"_position\":{\"x\":655.0,\"y\":2737.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.AppendVfxNode\",\"ownerOnly\":true,\"vfxName\":\"Vfx_006_Diana_00_Passive_Target\",\"boneType\":1,\"customBoneId\":0,\"offsetX\":0.0,\"offsetY\":0.0},{\"_id\":615564,\"_position\":{\"x\":176.0,\"y\":2739.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.OnObjectTickNode\"},{\"_id\":120299,\"_position\":{\"x\":973.0,\"y\":2735.0},\"_type\":\"Physalia.Flexi.LogNode\",\"text\":\"\u5237\u7279\u6548\"},{\"_id\":13031,\"_position\":{\"x\":1291.0,\"y\":1425.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SetProjectileNoHitNode\"},{\"_id\":667832,\"_position\":{\"x\":174.0,\"y\":3031.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.OnTickNode\",\"ignoreStun\":true,\"ignoreKnockedDown\":true},{\"_id\":630836,\"_position\":{\"x\":708.0,\"y\":3034.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.AppendVfxNode\",\"targetsOnly\":true,\"vfxName\":\"Vfx_006_Diana_00_Passive_Targeted\",\"boneType\":1,\"customBoneId\":0,\"offsetX\":0.0,\"offsetY\":0.0}],\"edges\":[{\"id1\":483943,\"port1\":\"next\",\"id2\":653660,\"port2\":\"previousPort\"},{\"id1\":483943,\"port1\":\"caster\",\"id2\":197830,\"port2\":\"source\"},{\"id1\":653660,\"port1\":\"truePort\",\"id2\":258629,\"port2\":\"previous\"},{\"id1\":653660,\"port1\":\"falsePort\",\"id2\":129628,\"port2\":\"previous\"},{\"id1\":933268,\"port1\":\"result\",\"id2\":653660,\"port2\":\"conditionPort\"},{\"id1\":276503,\"port1\":\"unitId\",\"id2\":129628,\"port2\":\"value\"},{\"id1\":276503,\"port1\":\"unitId\",\"id2\":256131,\"port2\":\"b\"},{\"id1\":146972,\"port1\":\"character\",\"id2\":276503,\"port2\":\"target\"},{\"id1\":256131,\"port1\":\"result\",\"id2\":408102,\"port2\":\"text\"},{\"id1\":197830,\"port1\":\"result\",\"id2\":933268,\"port2\":\"a\"},{\"id1\":235664,\"port1\":\"result\",\"id2\":933268,\"port2\":\"b\"},{\"id1\":959405,\"port1\":\"character\",\"id2\":235664,\"port2\":\"source\"},{\"id1\":128041,\"port1\":\"next\",\"id2\":395120,\"port2\":\"previousPort\"},{\"id1\":128041,\"port1\":\"entity\",\"id2\":629322,\"port2\":\"target\"},{\"id1\":128041,\"port1\":\"entity\",\"id2\":508307,\"port2\":\"target\"},{\"id1\":395120,\"port1\":\"truePort\",\"id2\":921000,\"port2\":\"previousPort\"},{\"id1\":44525,\"port1\":\"result\",\"id2\":395120,\"port2\":\"conditionPort\"},{\"id1\":921000,\"port1\":\"truePort\",\"id2\":580244,\"port2\":\"previous\"},{\"id1\":17616,\"port1\":\"result\",\"id2\":921000,\"port2\":\"conditionPort\"},{\"id1\":101027,\"port1\":\"y\",\"id2\":17616,\"port2\":\"a\"},{\"id1\":538652,\"port1\":\"value\",\"id2\":17616,\"port2\":\"b\"},{\"id1\":173884,\"port1\":\"result\",\"id2\":101027,\"port2\":\"value\"},{\"id1\":629322,\"port1\":\"result\",\"id2\":173884,\"port2\":\"a\"},{\"id1\":508307,\"port1\":\"result\",\"id2\":173884,\"port2\":\"b\"},{\"id1\":788413,\"port1\":\"value\",\"id2\":44525,\"port2\":\"a\"},{\"id1\":454863,\"port1\":\"next\",\"id2\":13031,\"port2\":\"previous\"},{\"id1\":364830,\"port1\":\"truePort\",\"id2\":454863,\"port2\":\"previous\"},{\"id1\":769013,\"port1\":\"entity\",\"id2\":454863,\"port2\":\"entity\"},{\"id1\":45205,\"port1\":\"value\",\"id2\":454863,\"port2\":\"pathArg1\"},{\"id1\":266230,\"port1\":\"result\",\"id2\":454863,\"port2\":\"pathArg2\"},{\"id1\":640506,\"port1\":\"y\",\"id2\":454863,\"port2\":\"pathArg3\"},{\"id1\":13031,\"port1\":\"next\",\"id2\":244623,\"port2\":\"previousPort\"},{\"id1\":769013,\"port1\":\"entity\",\"id2\":13031,\"port2\":\"entity\"},{\"id1\":244623,\"port1\":\"truePort\",\"id2\":523295,\"port2\":\"previous\"},{\"id1\":8142,\"port1\":\"result\",\"id2\":244623,\"port2\":\"conditionPort\"},{\"id1\":753867,\"port1\":\"result\",\"id2\":8142,\"port2\":\"a\"},{\"id1\":932744,\"port1\":\"value\",\"id2\":8142,\"port2\":\"b\"},{\"id1\":104461,\"port1\":\"result\",\"id2\":753867,\"port2\":\"source\"},{\"id1\":640506,\"port1\":\"x\",\"id2\":104461,\"port2\":\"a\"},{\"id1\":912811,\"port1\":\"x\",\"id2\":104461,\"port2\":\"b\"},{\"id1\":640506,\"port1\":\"x\",\"id2\":266230,\"port2\":\"a\"},{\"id1\":98120,\"port1\":\"result\",\"id2\":640506,\"port2\":\"value\"},{\"id1\":769013,\"port1\":\"entity\",\"id2\":98120,\"port2\":\"target\"},{\"id1\":769013,\"port1\":\"next\",\"id2\":364830,\"port2\":\"previousPort\"},{\"id1\":420739,\"port1\":\"result\",\"id2\":364830,\"port2\":\"conditionPort\"},{\"id1\":855937,\"port1\":\"value\",\"id2\":420739,\"port2\":\"a\"},{\"id1\":467605,\"port1\":\"center\",\"id2\":912811,\"port2\":\"value\"},{\"id1\":121579,\"port1\":\"result\",\"id2\":467605,\"port2\":\"target\"},{\"id1\":849271,\"port1\":\"value\",\"id2\":121579,\"port2\":\"unitId\"},{\"id1\":700043,\"port1\":\"result\",\"id2\":602305,\"port2\":\"text\"},{\"id1\":212112,\"port1\":\"next\",\"id2\":1797,\"port2\":\"previousPort\"},{\"id1\":212112,\"port1\":\"entity\",\"id2\":683728,\"port2\":\"entity\"},{\"id1\":1797,\"port1\":\"truePort\",\"id2\":683728,\"port2\":\"previous\"},{\"id1\":651229,\"port1\":\"result\",\"id2\":1797,\"port2\":\"conditionPort\"},{\"id1\":500632,\"port1\":\"value\",\"id2\":683728,\"port2\":\"pathArg1\"},{\"id1\":738798,\"port1\":\"x\",\"id2\":683728,\"port2\":\"pathArg2\"},{\"id1\":738798,\"port1\":\"y\",\"id2\":683728,\"port2\":\"pathArg3\"},{\"id1\":68303,\"port1\":\"center\",\"id2\":738798,\"port2\":\"value\"},{\"id1\":313558,\"port1\":\"result\",\"id2\":68303,\"port2\":\"target\"},{\"id1\":313558,\"port1\":\"result\",\"id2\":693736,\"port2\":\"targets\"},{\"id1\":313558,\"port1\":\"result\",\"id2\":630836,\"port2\":\"targets\"},{\"id1\":574908,\"port1\":\"value\",\"id2\":313558,\"port2\":\"unitId\"},{\"id1\":693736,\"port1\":\"next\",\"id2\":120299,\"port2\":\"previous\"},{\"id1\":615564,\"port1\":\"next\",\"id2\":693736,\"port2\":\"previous\"},{\"id1\":667832,\"port1\":\"next\",\"id2\":630836,\"port2\":\"previous\"},{\"id1\":320258,\"port1\":\"value\",\"id2\":651229,\"port2\":\"a\"},{\"id1\":369375,\"port1\":\"result\",\"id2\":137614,\"port2\":\"text\"},{\"id1\":929931,\"port1\":\"character\",\"id2\":89840,\"port2\":\"b\"},{\"id1\":89840,\"port1\":\"result\",\"id2\":433502,\"port2\":\"text\"},{\"id1\":949331,\"port1\":\"next\",\"id2\":433502,\"port2\":\"previous\"},{\"id1\":508505,\"port1\":\"next\",\"id2\":949331,\"port2\":\"previous\"},{\"id1\":369049,\"port1\":\"result\",\"id2\":949331,\"port2\":\"text\"},{\"id1\":733224,\"port1\":\"result\",\"id2\":508505,\"port2\":\"text\"},{\"id1\":505928,\"port1\":\"result\",\"id2\":733224,\"port2\":\"b\"},{\"id1\":258273,\"port1\":\"character\",\"id2\":369049,\"port2\":\"b\"}]}"
    - "{\"_type\":\"Physalia.Flexi.AbilityGraph\",\"nodes\":[{\"_id\":844646,\"_position\":{\"x\":1211.0,\"y\":58.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SetProjectileSpeedNode\"},{\"_id\":476645,\"_position\":{\"x\":192.0,\"y\":284.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.OnObjectTickNode\"},{\"_id\":428731,\"_position\":{\"x\":705.0,\"y\":281.0},\"_type\":\"Physalia.Flexi.IfElseNode\"},{\"_id\":65279,\"_position\":{\"x\":479.0,\"y\":517.0},\"_type\":\"Physalia.Flexi.GreaterOrEqualNode\",\"b\":1},{\"_id\":423991,\"_position\":{\"x\":227.0,\"y\":519.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetParamNode\",\"index\":1},{\"_id\":812664,\"_position\":{\"x\":1030.0,\"y\":282.0},\"_type\":\"Physalia.Flexi.IfElseNode\"},{\"_id\":86658,\"_position\":{\"x\":180.0,\"y\":706.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetParamNode\"},{\"_id\":4033,\"_position\":{\"x\":353.0,\"y\":706.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetUnitByIdNode\"},{\"_id\":764539,\"_position\":{\"x\":531.0,\"y\":706.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetCharacterCenterNode\"},{\"_id\":358981,\"_position\":{\"x\":691.0,\"y\":464.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetObjectPositionNode\"},{\"_id\":302522,\"_position\":{\"x\":1334.0,\"y\":462.0},\"_type\":\"Physalia.Flexi.LessOrEqualNode\"},{\"_id\":562360,\"_position\":{\"x\":1115.0,\"y\":605.0},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"distanceX0\"},{\"_id\":889845,\"_position\":{\"x\":796.0,\"y\":32.0},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"speed1\"},{\"_id\":947777,\"_position\":{\"x\":1709.0,\"y\":304.0},\"_type\":\"Physalia.Flexi.IfElseNode\"},{\"_id\":235406,\"_position\":{\"x\":1563.0,\"y\":460.0},\"_type\":\"Physalia.Flexi.LessOrEqualNode\"},{\"_id\":343138,\"_position\":{\"x\":1398.0,\"y\":598.0},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"distanceX\"},{\"_id\":277022,\"_position\":{\"x\":1977.0,\"y\":69.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SetProjectileSpeedNode\"},{\"_id\":169780,\"_position\":{\"x\":1562.0,\"y\":43.0},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"speed0\"},{\"_id\":728233,\"_position\":{\"x\":2047.0,\"y\":329.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.SetProjectileSpeedNode\"},{\"_id\":795727,\"_position\":{\"x\":1820.0,\"y\":617.0},\"_type\":\"Physalia.Flexi.BlackboardNode\",\"key\":\"speed\"},{\"_id\":914585,\"_position\":{\"x\":361.0,\"y\":148.0},\"_type\":\"Physalia.Flexi.StringNode\",\"text\":\"\u4F9D\u8DDD\u96E2\u8ABF\u6574\u901F\u5EA6\"},{\"_id\":809820,\"_position\":{\"x\":1069.0,\"y\":728.0},\"_type\":\"Physalia.Flexi.IntSubtractNode\"},{\"_id\":610603,\"_position\":{\"x\":1281.0,\"y\":729.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.AbsNode\"},{\"_id\":419698,\"_position\":{\"x\":894.0,\"y\":466.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetVector2XYNode\"},{\"_id\":975400,\"_position\":{\"x\":757.0,\"y\":703.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.GetVector2XYNode\"}],\"edges\":[{\"id1\":812664,\"port1\":\"truePort\",\"id2\":844646,\"port2\":\"previous\"},{\"id1\":476645,\"port1\":\"entity\",\"id2\":844646,\"port2\":\"entity\"},{\"id1\":889845,\"port1\":\"value\",\"id2\":844646,\"port2\":\"speed\"},{\"id1\":812664,\"port1\":\"falsePort\",\"id2\":947777,\"port2\":\"previousPort\"},{\"id1\":428731,\"port1\":\"truePort\",\"id2\":812664,\"port2\":\"previousPort\"},{\"id1\":302522,\"port1\":\"result\",\"id2\":812664,\"port2\":\"conditionPort\"},{\"id1\":947777,\"port1\":\"truePort\",\"id2\":277022,\"port2\":\"previous\"},{\"id1\":947777,\"port1\":\"falsePort\",\"id2\":728233,\"port2\":\"previous\"},{\"id1\":235406,\"port1\":\"result\",\"id2\":947777,\"port2\":\"conditionPort\"},{\"id1\":476645,\"port1\":\"entity\",\"id2\":277022,\"port2\":\"entity\"},{\"id1\":169780,\"port1\":\"value\",\"id2\":277022,\"port2\":\"speed\"},{\"id1\":476645,\"port1\":\"next\",\"id2\":428731,\"port2\":\"previousPort\"},{\"id1\":476645,\"port1\":\"entity\",\"id2\":358981,\"port2\":\"target\"},{\"id1\":476645,\"port1\":\"entity\",\"id2\":728233,\"port2\":\"entity\"},{\"id1\":65279,\"port1\":\"result\",\"id2\":428731,\"port2\":\"conditionPort\"},{\"id1\":423991,\"port1\":\"value\",\"id2\":65279,\"port2\":\"a\"},{\"id1\":358981,\"port1\":\"result\",\"id2\":419698,\"port2\":\"value\"},{\"id1\":419698,\"port1\":\"x\",\"id2\":809820,\"port2\":\"a\"},{\"id1\":809820,\"port1\":\"result\",\"id2\":610603,\"port2\":\"source\"},{\"id1\":975400,\"port1\":\"x\",\"id2\":809820,\"port2\":\"b\"},{\"id1\":610603,\"port1\":\"result\",\"id2\":302522,\"port2\":\"a\"},{\"id1\":610603,\"port1\":\"result\",\"id2\":235406,\"port2\":\"a\"},{\"id1\":562360,\"port1\":\"value\",\"id2\":302522,\"port2\":\"b\"},{\"id1\":343138,\"port1\":\"value\",\"id2\":235406,\"port2\":\"b\"},{\"id1\":764539,\"port1\":\"center\",\"id2\":975400,\"port2\":\"value\"},{\"id1\":4033,\"port1\":\"result\",\"id2\":764539,\"port2\":\"target\"},{\"id1\":86658,\"port1\":\"value\",\"id2\":4033,\"port2\":\"unitId\"},{\"id1\":795727,\"port1\":\"value\",\"id2\":728233,\"port2\":\"speed\"}]}"
  - graphs:
    - "{\"_type\":\"Physalia.Flexi.AbilityGraph\",\"nodes\":[{\"_id\":490387,\"_position\":{\"x\":80.0,\"y\":159.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.OnProjectileApplyStunNode\"},{\"_id\":614265,\"_position\":{\"x\":463.0,\"y\":159.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.AbilityLogNode\",\"eventType\":2,\"param2\":1},{\"_id\":585998,\"_position\":{\"x\":91.0,\"y\":500.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.OnObjectBreakOther\"},{\"_id\":765697,\"_position\":{\"x\":463.0,\"y\":500.0},\"_type\":\"Fanimax.CP.Gameplay.AbilityServices.AbilityLogNode\",\"eventType\":11,\"param2\":1},{\"_id\":418354,\"_position\":{\"x\":80.0,\"y\":36.0},\"_type\":\"Physalia.Flexi.StringNode\",\"text\":\"Todo:
      \u5F85\u88DC\u6253\u5230\u9632\u8B77\u7F69\u7684\u60C5\u6CC1\"}],\"edges\":[{\"id1\":490387,\"port1\":\"next\",\"id2\":614265,\"port2\":\"previous\"},{\"id1\":490387,\"port1\":\"instigator\",\"id2\":614265,\"port2\":\"instigator\"},{\"id1\":490387,\"port1\":\"target\",\"id2\":614265,\"port2\":\"targets\"},{\"id1\":585998,\"port1\":\"next\",\"id2\":765697,\"port2\":\"previous\"},{\"id1\":585998,\"port1\":\"caster\",\"id2\":765697,\"port2\":\"instigator\"}]}"
