using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine.Networking;
using System.Net.Sockets;
using PlayFab;
using System.Linq;
using System.Globalization;

namespace Fanimax.CP
{
    public struct ServerMaintenanceTimeStampData
    {
        public long startTimeStamp;
        public long endTimeStamp;
        public string[] whiteListPlayerIds;
        public string[] insideIPs;
    }

    public class ServerMaintenanceTimeManager : IServerMaintenanceTimeManager
    {
        private static readonly Logger.Label Label = Logger.Label.CreateFromCurrentClass();

        private const string ServerMaintainTimestampKey = "ServerMaintainTimestamp";

        private readonly IGameDataManager _gameDataManager;

        private DateTime? _serverMaintainTime;
        private DateTime? _serverMaintainEndTime;
        private DateTime _lastFetchTime;

        public bool IsServerMaintain => !IsWhiteListPlayerId && !IsInsideIP && IsDuringServerMaintain;
        public DateTime? ServerMaintainEndDevTime => _serverMaintainEndTime?.UtcToDevTime();

        private bool IsDuringServerMaintain => _serverMaintainTime != null && TimeUtility.UtcNow.IsExpired(_serverMaintainTime.Value) && (_serverMaintainEndTime == null || !TimeUtility.UtcNow.IsExpired(_serverMaintainEndTime.Value));
        private string _ipAddress; // 預留給白名單使用
        public string ClientIPAddress { get => _ipAddress; set => _ipAddress = value; }
        private string[] _insideIPs;
        private bool IsInsideIP => _ipAddress != null && _insideIPs.Contains(_ipAddress);

        private string PlayFabId => PlayFabSettings.staticPlayer.PlayFabId; // 公司中的唯一 ID
        private string PlayerId => PlayFabSettings.staticPlayer.EntityId; // 遊戲中的唯一 ID
        private string[] _whiteListPlayerIds;
        private bool IsWhiteListPlayerId => _whiteListPlayerIds != null && _whiteListPlayerIds.Contains(PlayerId);

        public ServerMaintenanceTimeManager(IGameDataManager gameDataManager)
        {
            _gameDataManager = gameDataManager;
        }

        public async UniTask<Result> FetchServerMaintenanceTimeAsync(CancellationToken token)
        {
            _ipAddress = await GetClientIPAddress(); // 預留給白名單檢查內部 ip 使用
            
            Logger.Debug(Label, $"<b>FetchServerMaintenanceTimeAsync</b> => start !");
            {
                int maintainanceRefreshCooldown = _gameDataManager.GetSettingTable<GlobalSetting>().MaintenanceRefreshCooldown;
                if (!TimeUtility.UtcNow.IsExpired(_lastFetchTime.AddMinutes(maintainanceRefreshCooldown)))
                {
                    return new Result();
                }
            }

            {
                Result<ServerMaintenanceTimeStampData> getServerMaintainTimestampResult = await PlayfabUtility.GetTitleDataAsync<ServerMaintenanceTimeStampData>(ServerMaintainTimestampKey);

                if (token.IsCancellationRequested)
                {
                    Logger.Debug(Label, $"<b>FetchServerMaintainTimestamp</b> => is canceled");
                    return null;
                }

                if (getServerMaintainTimestampResult.IsFailure)
                {
                    switch (getServerMaintainTimestampResult.ErrorCode)
                    {
                        case ErrorCode.ServiceNotAuthorized:
                            _lastFetchTime = TimeUtility.UtcNow;
                            _serverMaintainTime = DateTime.MinValue;
                            _serverMaintainEndTime = null;
                            break;
                    }

                    return getServerMaintainTimestampResult;
                }

                _lastFetchTime = TimeUtility.UtcNow;
                _serverMaintainTime = getServerMaintainTimestampResult.Value.startTimeStamp > 0 ? getServerMaintainTimestampResult.Value.startTimeStamp.ToDateTime() : null;
                _serverMaintainEndTime = getServerMaintainTimestampResult.Value.endTimeStamp > 0 ? getServerMaintainTimestampResult.Value.endTimeStamp.ToDateTime() : null;

                var startStr = _serverMaintainTime?.UtcToDevTime().ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture) ?? "-";
                var endStr = _serverMaintainEndTime?.UtcToDevTime().ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture) ?? "-";
                Logger.Debug(Label, $"<b>FetchServerMaintenanceTimeAsync</b> => 維護時間: {startStr}, 結束時間: {endStr}");
                _whiteListPlayerIds = getServerMaintainTimestampResult.Value.whiteListPlayerIds;

                if (_whiteListPlayerIds != null && _whiteListPlayerIds.Length > 0)
                {
                    if (IsDuringServerMaintain && IsWhiteListPlayerId)
                    {
                        Logger.Debug(Label, $"<b>FetchServerMaintenanceTimeAsync</b> => 維護中, 但{PlayFabId}在白名單中, 可以進入遊戲中 !!!");
                    }
                }

                _insideIPs = getServerMaintainTimestampResult.Value.insideIPs;
                Logger.Debug(Label, $"<b>Fetch IP Async</b> => {_ipAddress}, IsInsideIP = {IsInsideIP}");
                if (IsDuringServerMaintain && IsInsideIP)
                    Logger.Debug(Label, $"<b>FetchServerMaintenanceTimeAsync</b> => 維護中, 但利用內部IP : {_ipAddress} 進入 !!!");
            }

            return new Result();
        }

        public void Clear()
        {
            _serverMaintainTime = default;
            _serverMaintainEndTime = default;
            _lastFetchTime = default;
            _ipAddress = null;
            _whiteListPlayerIds = null;
        }
        
        /// <summary>
        /// 獲取客戶端的公網 IP 地址
        /// </summary>
        private async UniTask<string> GetClientIPAddress()
        {
            try
            {
                // 方法一：使用外部服務獲取公網 IP（推薦）
                string publicIP = await GetPublicIPAsync();
                if (!string.IsNullOrEmpty(publicIP))
                {
                    return publicIP;
                }
            }
            catch (Exception ex)
            {
                Logger.Warn($"[GetClientIPAddress] Failed to get public IP: {ex.Message}");
            }
            
            try
            {
                // 方法二：獲取本地網路 IP 作為備用
                string localIP = GetLocalIPAddress();
                if (!string.IsNullOrEmpty(localIP))
                {
                    return localIP;
                }
            }
            catch (Exception ex)
            {
                Logger.Warn($"[GetClientIPAddress] Failed to get local IP: {ex.Message}");
            }
            
            // 如果都失敗，返回預設值
            return "0.0.0.0";
        }
        
        /// <summary>
        /// 透過外部服務獲取公網 IP 地址
        /// </summary>
        private async UniTask<string> GetPublicIPAsync()
        {
            string[] ipServices = {
                "https://api.ipify.org",
                "https://icanhazip.com",
                "https://ipinfo.io/ip",
                "https://checkip.amazonaws.com"
            };
            
            foreach (string service in ipServices)
            {
                try
                {
                    using (UnityWebRequest request = UnityWebRequest.Get(service))
                    {
                        request.timeout = 5; // 5秒超時
                        
                        await request.SendWebRequest();
                        
                        if (request.result == UnityWebRequest.Result.Success)
                        {
                            string ip = request.downloadHandler.text.Trim();
                            
                            // 驗證 IP 格式
                            if (System.Net.IPAddress.TryParse(ip, out _))
                            {
                                Logger.Debug($"[GetPublicIPAsync] Got public IP from {service}: {ip}");
                                return ip;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Logger.Warn($"[GetPublicIPAsync] Service {service} failed: {ex.Message}");
                    continue;
                }
            }
            
            return string.Empty;
        }
        
        /// <summary>
        /// 獲取本地網路 IP 地址
        /// </summary>
        private string GetLocalIPAddress()
        {
            try
            {
                // 獲取本機 IP 地址
                using (Socket socket = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, 0))
                {
                    socket.Connect("*******", 80); // 連接到 Google DNS
                    System.Net.IPEndPoint endPoint = socket.LocalEndPoint as System.Net.IPEndPoint;
                    string localIP = endPoint?.Address.ToString();
                    
                    if (!string.IsNullOrEmpty(localIP) && localIP != "127.0.0.1")
                    {
                        Logger.Debug($"[GetLocalIPAddress] Got local IP: {localIP}");
                        return localIP;
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Warn($"[GetLocalIPAddress] Failed: {ex.Message}");
            }
            
            return string.Empty;
        }
    }
}
