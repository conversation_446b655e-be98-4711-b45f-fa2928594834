%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45f4116f2cdfba14b8cf00f26426a3d4, type: 3}
  m_Name: Ability_100101
  m_EditorClassIdentifier: 
  blackboard:
  - key: boostSpeed
    value: 0
  - key: rangeX
    value: 0
  - key: rangeY
    value: 0
  graphGroups:
  - graphs:
    - nodes:
      - rid: 8923418986610950324
      - rid: 8923418986610950325
      - rid: 8923418986610950326
      - rid: 8923418986610950327
      - rid: 8923418986610950328
      - rid: 8923418986610950329
      - rid: 8923418986610950330
      - rid: 8923418986610950331
      - rid: 8923418986610950332
      - rid: 8923418986610950333
      - rid: 8923418986610950334
      - rid: 8923418986610950335
      - rid: 8923418986610950336
      - rid: 8923418986610950337
      - rid: 8923418986610950338
      - rid: 8923418986610950339
      - rid: 8923418986610950340
      - rid: 8923418986610950341
      - rid: 8923418986610950342
      - rid: 8923418986610950343
      - rid: 8923418986610950344
      - rid: 8923418986610950345
      - rid: 8923418986610950346
      - rid: 8923418986610950347
      - rid: 8923418986610950348
      - rid: 8923418986610950349
      - rid: 8923418986610950350
      - rid: 8923418986610950351
      - rid: 8923418986610950352
      - rid: 8923418986610950353
      - rid: 8923418986610950354
      - rid: 8923418986610950355
      - rid: 8923418986610950356
      edges:
      - id1: 540268
        port1: result
        id2: 574631
        port2: conditionPort
      - id1: 248408
        port1: next
        id2: 574631
        port2: previousPort
      - id1: 574631
        port1: truePort
        id2: 93390
        port2: previousPort
      - id1: 696917
        port1: character
        id2: 540268
        port2: source
      - id1: 93390
        port1: truePort
        id2: 223067
        port2: previous
      - id1: 223067
        port1: next
        id2: 13474
        port2: previous
      - id1: 877733
        port1: value
        id2: 223067
        port2: speedMaxModifier
      - id1: 986400
        port1: characters
        id2: 251789
        port2: collectionPort
      - id1: 476151
        port1: character
        id2: 986400
        port2: source
      - id1: 720028
        port1: result
        id2: 93390
        port2: conditionPort
      - id1: 720028
        port1: result
        id2: 857223
        port2: conditionPort
      - id1: 251789
        port1: countPort
        id2: 720028
        port2: a
      - id1: 494923
        port1: value
        id2: 986400
        port2: width
      - id1: 685042
        port1: value
        id2: 986400
        port2: height
      - id1: 587388
        port1: character
        id2: 13474
        port2: targets
      - id1: 746650
        port1: next
        id2: 346509
        port2: previous
      - id1: 60614
        port1: next
        id2: 857223
        port2: previousPort
      - id1: 857223
        port1: truePort
        id2: 960629
        port2: previous
      - id1: 857223
        port1: falsePort
        id2: 372619
        port2: previousPort
      - id1: 44689
        port1: amount
        id2: 346509
        port2: param1
      - id1: 997043
        port1: character
        id2: 44689
        port2: target
      - id1: 33550
        port1: character
        id2: 960629
        port2: targets
      - id1: 372619
        port1: truePort
        id2: 470644
        port2: previous
      - id1: 627314
        port1: result
        id2: 372619
        port2: conditionPort
      - id1: 485957
        port1: character
        id2: 627314
        port2: source
      - id1: 105869
        port1: character
        id2: 470644
        port2: targets
      - id1: 660630
        port1: amount
        id2: 346509
        port2: param2
      - id1: 75185
        port1: character
        id2: 660630
        port2: target
      - id1: 567932
        port1: character
        id2: 346509
        port2: instigator
      - id1: 799204
        port1: characters
        id2: 986400
        port2: excludes
      - id1: 158308
        port1: character
        id2: 799204
        port2: source
  references:
    version: 2
    RefIds:
    - rid: 8923418986610950324
      type: {class: IsCyberBoostingNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 540268
    - rid: 8923418986610950325
      type: {class: StatRefreshEventNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 248408
        order: 1
    - rid: 8923418986610950326
      type: {class: IfElseNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 574631
        conditionPort: 0
    - rid: 8923418986610950327
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 696917
    - rid: 8923418986610950328
      type: {class: IfElseNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 93390
        conditionPort: 0
    - rid: 8923418986610950329
      type: {class: BoostModifierNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 223067
        speedMaxModifier: 0
        accelerationModifier: 0
        isCyber: 0
        isParkourPoint: 0
    - rid: 8923418986610950330
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 877733
        key: boostSpeed
    - rid: 8923418986610950331
      type: {class: GetCharactersAtForwardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 986400
        width: 0
        height: 0
        offsetX: 0
        offsetY: 0
        angle: 0
    - rid: 8923418986610950332
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 476151
    - rid: 8923418986610950333
      type: {class: GreaterNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 720028
        a: 0
        b: 0
    - rid: 8923418986610950334
      type: {class: CountNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 251789
    - rid: 8923418986610950335
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 494923
        key: rangeX
    - rid: 8923418986610950336
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 685042
        key: rangeY
    - rid: 8923418986610950337
      type: {class: AppendVfxNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 13474
        ownerOnly: 0
        targetsOnly: 0
        vfxName: Vfx_001_Park_00_Passive_CatchUpRunner
        sfxName: 
        boneType: 0
        customBoneId: 0
        offsetX: 0
        offsetY: 0
    - rid: 8923418986610950338
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 587388
    - rid: 8923418986610950339
      type: {class: OnPassedProgressLineNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 746650
        ignoreStun: 0
        ignoreKnockedDown: 0
        progressLine: 26
    - rid: 8923418986610950340
      type: {class: OnTickNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 60614
        ignoreStun: 1
        ignoreKnockedDown: 0
    - rid: 8923418986610950341
      type: {class: IfElseNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 857223
        conditionPort: 0
    - rid: 8923418986610950342
      type: {class: GetTokenNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 44689
        tokenId: 1001001
    - rid: 8923418986610950343
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 997043
    - rid: 8923418986610950344
      type: {class: TokenAddNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 960629
        tokenId: 1001001
        amount: 1
    - rid: 8923418986610950345
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 33550
    - rid: 8923418986610950346
      type: {class: IfElseNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 372619
        conditionPort: 0
    - rid: 8923418986610950347
      type: {class: IsCyberBoostingNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 627314
    - rid: 8923418986610950348
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 485957
    - rid: 8923418986610950349
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 105869
    - rid: 8923418986610950350
      type: {class: TokenAddNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 470644
        tokenId: 1001002
        amount: 2
    - rid: 8923418986610950351
      type: {class: GetTokenNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 660630
        tokenId: 1001002
    - rid: 8923418986610950352
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 75185
    - rid: 8923418986610950353
      type: {class: AbilityLogNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 346509
        eventType: 11
        param1: 0
        param2: 0
        param3: 0
        param4: 0
    - rid: 8923418986610950354
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 567932
    - rid: 8923418986610950355
      type: {class: GetAllCharactersNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 799204
        excludeAlly: 0
        excludeEnemy: 1
    - rid: 8923418986610950356
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 158308
