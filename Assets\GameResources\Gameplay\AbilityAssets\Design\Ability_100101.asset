%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 32f4e526f6c4d2a49979457203108d7a, type: 3}
  m_Name: Ability_100101
  m_EditorClassIdentifier: 
  blackboard:
  - key: boostSpeed
    value: 0
  - key: rangeX
    value: 0
  - key: rangeY
    value: 0
  graphJsons:
  - '{"_type":"Physalia.Flexi.AbilityGraph","nodes":[{"_id":775246,"_position":{"x":-44.0,"y":211.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.OnCastNode"},{"_id":145346,"_position":{"x":350.0,"y":213.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.AppendBoostNode"},{"_id":611588,"_position":{"x":157.0,"y":508.0},"_type":"Physalia.Flexi.BlackboardNode","key":"duration"},{"_id":60027,"_position":{"x":140.0,"y":410.0},"_type":"Physalia.Flexi.BlackboardNode","key":"boostAccel"},{"_id":478235,"_position":{"x":136.0,"y":312.0},"_type":"Physalia.Flexi.BlackboardNode","key":"boostSpeed"},{"_id":465553,"_position":{"x":128.0,"y":110.0},"_type":"Physalia.Flexi.IfElseNode"}],"edges":[{"id1":775246,"port1":"next","id2":465553,"port2":"previousPort"},{"id1":775246,"port1":"caster","id2":145346,"port2":"targets"},{"id1":465553,"port1":"truePort","id2":145346,"port2":"previous"},{"id1":478235,"port1":"value","id2":145346,"port2":"initialSpeed"},{"id1":478235,"port1":"value","id2":145346,"port2":"speedMaxModifier"},{"id1":60027,"port1":"value","id2":145346,"port2":"accelerationModifier"},{"id1":611588,"port1":"value","id2":145346,"port2":"duration"}]}'
  graphGroups:
  - graphs:
    - '{"_type":"Physalia.Flexi.AbilityGraph","nodes":[{"_id":540268,"_position":{"x":524.0,"y":17.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.IsCyberBoostingNode"},{"_id":248408,"_position":{"x":400.0,"y":-100.0},"_type":"Physalia.Flexi.StatRefreshEventNode","order":1},{"_id":574631,"_position":{"x":705.0,"y":-100.0},"_type":"Physalia.Flexi.IfElseNode"},{"_id":696917,"_position":{"x":398.0,"y":17.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":93390,"_position":{"x":1047.0,"y":-103.0},"_type":"Physalia.Flexi.IfElseNode"},{"_id":223067,"_position":{"x":1337.0,"y":-103.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.BoostModifierNode"},{"_id":877733,"_position":{"x":1148.0,"y":17.0},"_type":"Physalia.Flexi.BlackboardNode","key":"boostSpeed"},{"_id":986400,"_position":{"x":400.0,"y":115.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.GetCharactersAtForwardNode"},{"_id":476151,"_position":{"x":249.0,"y":115.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":720028,"_position":{"x":852.0,"y":118.0},"_type":"Physalia.Flexi.GreaterNode"},{"_id":251789,"_position":{"x":653.0,"y":118.0},"_type":"Physalia.Flexi.CountNode"},{"_id":494923,"_position":{"x":227.0,"y":217.0},"_type":"Physalia.Flexi.BlackboardNode","key":"rangeX"},{"_id":685042,"_position":{"x":227.0,"y":315.0},"_type":"Physalia.Flexi.BlackboardNode","key":"rangeY"},{"_id":13474,"_position":{"x":1806.0,"y":-103.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.AppendVfxNode","vfxName":"Vfx_001_Park_00_Passive_CatchUpRunner","boneType":0,"customBoneId":0,"offsetX":0.0,"offsetY":0.0},{"_id":587388,"_position":{"x":1678.0,"y":-187.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":746650,"_position":{"x":397.0,"y":915.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.OnPassedProgressLineNode","progressLine":26},{"_id":60614,"_position":{"x":400.0,"y":427.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.OnTickNode","ignoreStun":true},{"_id":857223,"_position":{"x":1047.0,"y":424.0},"_type":"Physalia.Flexi.IfElseNode"},{"_id":44689,"_position":{"x":725.0,"y":1076.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.GetTokenNode","tokenId":1001001},{"_id":997043,"_position":{"x":554.0,"y":1076.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":960629,"_position":{"x":1714.0,"y":424.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.TokenAddNode","tokenId":1001001,"amount":1},{"_id":33550,"_position":{"x":1595.0,"y":573.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":372619,"_position":{"x":1338.0,"y":666.0},"_type":"Physalia.Flexi.IfElseNode"},{"_id":627314,"_position":{"x":1123.0,"y":666.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.IsCyberBoostingNode"},{"_id":485957,"_position":{"x":997.0,"y":666.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":105869,"_position":{"x":1595.0,"y":815.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":470644,"_position":{"x":1714.0,"y":666.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.TokenAddNode","tokenId":1001002,"amount":2},{"_id":660630,"_position":{"x":725.0,"y":1177.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.GetTokenNode","tokenId":1001002},{"_id":75185,"_position":{"x":554.0,"y":1177.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":346509,"_position":{"x":963.0,"y":915.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.AbilityLogNode","eventType":11},{"_id":567932,"_position":{"x":788.0,"y":999.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":799204,"_position":{"x":-26.0,"y":143.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.GetAllCharactersNode","excludeEnemy":true},{"_id":158308,"_position":{"x":-181.0,"y":143.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"}],"edges":[{"id1":540268,"port1":"result","id2":574631,"port2":"conditionPort"},{"id1":696917,"port1":"character","id2":540268,"port2":"source"},{"id1":574631,"port1":"truePort","id2":93390,"port2":"previousPort"},{"id1":248408,"port1":"next","id2":574631,"port2":"previousPort"},{"id1":93390,"port1":"truePort","id2":223067,"port2":"previous"},{"id1":720028,"port1":"result","id2":93390,"port2":"conditionPort"},{"id1":223067,"port1":"next","id2":13474,"port2":"previous"},{"id1":877733,"port1":"value","id2":223067,"port2":"speedMaxModifier"},{"id1":587388,"port1":"character","id2":13474,"port2":"targets"},{"id1":720028,"port1":"result","id2":857223,"port2":"conditionPort"},{"id1":251789,"port1":"countPort","id2":720028,"port2":"a"},{"id1":857223,"port1":"truePort","id2":960629,"port2":"previous"},{"id1":857223,"port1":"falsePort","id2":372619,"port2":"previousPort"},{"id1":60614,"port1":"next","id2":857223,"port2":"previousPort"},{"id1":33550,"port1":"character","id2":960629,"port2":"targets"},{"id1":372619,"port1":"truePort","id2":470644,"port2":"previous"},{"id1":627314,"port1":"result","id2":372619,"port2":"conditionPort"},{"id1":105869,"port1":"character","id2":470644,"port2":"targets"},{"id1":485957,"port1":"character","id2":627314,"port2":"source"},{"id1":986400,"port1":"characters","id2":251789,"port2":"collectionPort"},{"id1":476151,"port1":"character","id2":986400,"port2":"source"},{"id1":799204,"port1":"characters","id2":986400,"port2":"excludes"},{"id1":494923,"port1":"value","id2":986400,"port2":"width"},{"id1":685042,"port1":"value","id2":986400,"port2":"height"},{"id1":158308,"port1":"character","id2":799204,"port2":"source"},{"id1":746650,"port1":"next","id2":346509,"port2":"previous"},{"id1":567932,"port1":"character","id2":346509,"port2":"instigator"},{"id1":44689,"port1":"amount","id2":346509,"port2":"param1"},{"id1":660630,"port1":"amount","id2":346509,"port2":"param2"},{"id1":997043,"port1":"character","id2":44689,"port2":"target"},{"id1":75185,"port1":"character","id2":660630,"port2":"target"}]}'
