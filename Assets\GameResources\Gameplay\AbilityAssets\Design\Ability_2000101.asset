%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 32f4e526f6c4d2a49979457203108d7a, type: 3}
  m_Name: Ability_2000101
  m_EditorClassIdentifier: 
  blackboard:
  - key: duration
    value: 0
  - key: boostSpeed
    value: 0
  - key: invulnerable
    value: 0
  - key: offsetX
    value: 0
  - key: offsetY
    value: 0
  - key: rangeX
    value: 0
  - key: rangeY
    value: 0
  - key: lifetime
    value: 0
  - key: stun
    value: 0
  - key: energyRecovery
    value: 0
  - key: impactY
    value: 0
  graphJsons: []
  graphGroups:
  - graphs:
    - '{"_type":"Physalia.Flexi.AbilityGraph","nodes":[{"_id":817886,"_position":{"x":405.0,"y":183.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.AppendAbilityNode","gameplayAbilityId":-1,"groupIndex":1,"ignoreClearWhenStunned":false,"showDurationHUD":false},{"_id":190302,"_position":{"x":183.0,"y":350.0},"_type":"Physalia.Flexi.BlackboardNode","key":"delay"},{"_id":771972,"_position":{"x":642.0,"y":305.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":225234,"_position":{"x":755.0,"y":183.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.AccelerateToMaxNode"},{"_id":861515,"_position":{"x":1609.0,"y":183.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.FinishNode"},{"_id":71643,"_position":{"x":1077.0,"y":183.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.PlayVfxNode","vfxName":"Vfx_CyberItem_ChargeableTrap","boneType":1,"customBoneId":0,"offsetX":1.75,"offsetY":3.125,"isInWorld":false},{"_id":740340,"_position":{"x":853.0,"y":398.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":541168,"_position":{"x":274.0,"y":95.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":652006,"_position":{"x":7.0,"y":183.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.OnActivated"},{"_id":358694,"_position":{"x":1366.0,"y":183.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.CueCVNode","abilityCVId":-2}],"edges":[{"id1":817886,"port1":"next","id2":225234,"port2":"previous"},{"id1":652006,"port1":"next","id2":817886,"port2":"previous"},{"id1":541168,"port1":"character","id2":817886,"port2":"targets"},{"id1":190302,"port1":"value","id2":817886,"port2":"ticks"},{"id1":225234,"port1":"next","id2":71643,"port2":"previous"},{"id1":771972,"port1":"character","id2":225234,"port2":"targets"},{"id1":71643,"port1":"next","id2":358694,"port2":"previous"},{"id1":740340,"port1":"character","id2":71643,"port2":"targets"},{"id1":358694,"port1":"next","id2":861515,"port2":"previous"}]}'
  - graphs:
    - '{"_type":"Physalia.Flexi.AbilityGraph","nodes":[{"_id":523310,"_position":{"x":982.0,"y":319.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.CreateAreaNode","deactivationTime":15,"canBreakProjectile":true,"vfxDataId":300710001,"hitFxDataId":3000100,"lockX":false,"lockY":false,"canHitLevelTrap":false,"canBreakLevelTrap":false,"ignoreHitIfPreviousHit":false,"shapeType":1,"pathType":9,"rootType":0,"pathArg1":0,"pathArg2":0,"pathArg3":0},{"_id":517379,"_position":{"x":775.0,"y":806.0},"_type":"Physalia.Flexi.BlackboardNode","key":"offsetY"},{"_id":965692,"_position":{"x":775.0,"y":708.0},"_type":"Physalia.Flexi.BlackboardNode","key":"offsetX"},{"_id":313730,"_position":{"x":775.0,"y":612.0},"_type":"Physalia.Flexi.BlackboardNode","key":"rangeY"},{"_id":76745,"_position":{"x":775.0,"y":514.0},"_type":"Physalia.Flexi.BlackboardNode","key":"rangeX"},{"_id":666721,"_position":{"x":775.0,"y":429.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":615584,"_position":{"x":546.0,"y":328.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.DelayNode","nextActionTickSlot":1,"countSlot":2},{"_id":452564,"_position":{"x":397.0,"y":612.0},"_type":"Physalia.Flexi.BlackboardNode","key":"delay"},{"_id":188590,"_position":{"x":134.0,"y":328.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.OnTickNode"},{"_id":19638,"_position":{"x":774.0,"y":1002.0},"_type":"Physalia.Flexi.BlackboardNode","key":"stun"},{"_id":542534,"_position":{"x":774.0,"y":1100.0},"_type":"Physalia.Flexi.BlackboardNode","key":"energyRecovery"},{"_id":117618,"_position":{"x":1322.0,"y":325.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.RemoveAbilityNode","gameplayAbilityId":-1,"groupIndex":1},{"_id":539198,"_position":{"x":1235.0,"y":208.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":738904,"_position":{"x":607.0,"y":687.0},"_type":"Physalia.Flexi.BlackboardNode","key":"lifetime"},{"_id":263426,"_position":{"x":166.0,"y":-86.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.OnTickNode"},{"_id":250012,"_position":{"x":505.0,"y":120.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.SelfNode"},{"_id":808376,"_position":{"x":658.0,"y":-86.0},"_type":"Fanimax.CP.Gameplay.AbilityServices.AppendVfxNode","vfxName":"Vfx_CyberItem_OneShotShield","boneType":0,"customBoneId":0,"offsetX":0.0,"offsetY":0.0}],"edges":[{"id1":523310,"port1":"next","id2":117618,"port2":"previous"},{"id1":615584,"port1":"next","id2":523310,"port2":"previous"},{"id1":666721,"port1":"character","id2":523310,"port2":"caster"},{"id1":666721,"port1":"character","id2":523310,"port2":"anchor"},{"id1":76745,"port1":"value","id2":523310,"port2":"size"},{"id1":313730,"port1":"value","id2":523310,"port2":"size2"},{"id1":965692,"port1":"value","id2":523310,"port2":"offsetX"},{"id1":517379,"port1":"value","id2":523310,"port2":"offsetY"},{"id1":738904,"port1":"value","id2":523310,"port2":"lifetime"},{"id1":19638,"port1":"value","id2":523310,"port2":"stunTicks"},{"id1":542534,"port1":"value","id2":523310,"port2":"energyGainForHit"},{"id1":539198,"port1":"character","id2":117618,"port2":"targets"},{"id1":188590,"port1":"next","id2":615584,"port2":"previous"},{"id1":452564,"port1":"value","id2":615584,"port2":"ticks"},{"id1":263426,"port1":"next","id2":808376,"port2":"previous"},{"id1":250012,"port1":"character","id2":808376,"port2":"targets"}]}'
