# ProjectCP – AI 協作者說明（Copilot Instructions）

本文件為 AI 代碼協# ProjectCP – AI 協作者說明（Copilot Instructions）

本文件為 AI 程式碼協作者提供速讀手冊，聚焦「本專案特有的結構、流程與慣例」。請在開始大型變更前先掃描此文與註解中的範例檔案。

## 1) 核心開發理念與工作流程 (非常重要！)
本專案採用**資料驅動設計 (Data-Driven Design)**。這是最關鍵的、必須遵守的開發模式。

- **原則**: **切勿在程式碼中寫死任何遊戲數值、設定或文字內容。** 所有可配置的資料都源於 `GameData/` 目錄下的 Excel 檔案。C# 腳本的職責是讀取這些資料並實現邏輯，而不是儲存資料。

- **關鍵工作流程：資料匯出**:
  1.  **修改 Excel**: 在 `GameData/` 中找到對應的 `.xlsx` 或 `.xlsm` 檔案進行編輯。
  2.  **執行匯出工具**: 執行 `OperationConsole/ProjectCP.Operation.exe`。此工具會讀取 Excel 檔案並生成 Unity 引擎可用的資料資產 (例如 ScriptableObjects)。
  3.  **驗證**: 執行後，相關的資料資產會被更新，通常位於 `Assets/GameResources/` (請依實際情況確認)。遊戲將讀取這些新生成的資產。

**範例**: 若要調整角色能力，你必須去修改 `GameData/AbilityData.xlsm`，然後執行匯出工具，而不是直接在 C# 程式碼中尋找並修改數值。

---

## 2) 大架構與模組邊界
- 主要代碼位於 `Assets/Scripts` 下，依功能分層：
  - `Common/`：跨域通用邏輯與基礎設施。
    - `Shared/Result.cs` 定義 Result / Result<T> 與 Error/ErrorCode，為全專案錯誤與流程控制的核心抽象；所有服務回傳都應以 `Result` 家族為主。
    - `Shared/ErrorCode.cs` 為集中枚舉，涵蓋 IAP、Ads、Matchmaking、Gameplay 等錯誤碼；請重用既有錯誤碼，避免新增重覆概念。
  - `GameData/`、`Gameplay/`、`GameplaySimulator/`、`GameplayTutorial/`：遊戲域場景與模擬。
  - `IAPService/`：內購整合（Unity IAP v5）。注意裝置地區 vs 帳號地區的貨幣顯示差異；UI 僅作為顯示參考，實際結算以商店原生視窗為準。
  - 其他資料夾（Authentication、Main、Protocols、Services、Utilities、CommonUI 等）對應子系統職責，遵守 Result/Logger 慣例。
- 網路架構（Photon Fusion）：
  - 多人遊戲使用 Fusion NetworkBehaviour，支援 Host/Client 與 Dedicated Server 模式
  - 核心網路類：`NetworkedPickupManager`、`NetworkedSwitchSystemManager`、`NetworkedTrapManager`
  - Fusion 容量限制：MaxAbilityCount/MaxStatusCount = 16，MaxTokenCount = 24（見 `GameplayConst.cs`）
  - 教學模式有獨立的 `GameplayModeTutorial` 網路實作
- 網路通訊協定與 API：
  - **PlayFab Azure Functions**：主要後端 API，透過 `PlayFabManager.Execute<T,K>()` 呼叫，支援自動重試與錯誤映射
  - **PlayFab Multiplayer API**：多人遊戲專用 API 模組（`Fanimax.PlayFabMultiplayer`）
    - Lobby 系統：`PlayFabLobbyAPI` 處理大廳創建、加入、離開與更新
    - Matchmaking 系統：`PlayFabMatchmakingAPI` 處理配對票券與區域延遲檢測
    - PubSub 即時訊息：`PlayFabPubSubManager` 基於 SignalR 的即時通訊
    - 速率限制處理：內建 RetryAfter 錯誤快取與自動重試機制
  - **Photon Chat**：即時聊天與線上狀態，使用 `ChatService` 管理連接與重連邏輯
  - **PlayFab PubSub**：即時訊息推送，用於配對、自訂房間等功能
  - **BestHTTP**：自訂 HTTP 請求，透過 `HttpManager` 封裝，支援 GET/POST 與重試機制
  - **UnityWebRequest**：基礎網路檢查（`NetworkUtility.CheckInternetConnection`）
  - **PlayFab QoS**：區域延遲檢測，透過 `PlayFabQosApi.GetQosResultAsync()` 執行 UDP ping 測試
    - 支援多區域並行延遲測試（預設 10 次 ping，250ms 超時）
    - 錯誤碼：`MatchmakingGetQosFailed (210)`、`MatchmakingGetNoValidRegionLatency (211)`
    - 用於配對系統的最佳區域選擇（`MatchingService.GetRegionLatencyWithRetryAsync()`）
  - **PlayFab 登入系統**：多平台身份驗證與帳號管理
    - 平台登入：支援 Anonymous、Google、Apple 三種登入方式
    - 帳號綁定：支援第三方帳號連結/解除連結操作
    - SessionTicket 管理：登入成功後設定 `PlayFabManager.SessionTicket`
    - 玩家資料同步：`GetPlayerDataAsync()` 取得玩家資料並設定 Token/LobbyToken
- 外部依賴（PlayFab、Photon/Fusion、Addressables、Spine 等）位於 `Packages/` 與 `Assets/ThirdParty/`；避免在這些檔案夾內直接修改第三方代碼。

## 3) 重要設計慣例
- 錯誤/流程控制：
  - 以 `Result`/`Result<T>` 傳遞成功與失敗，失敗時帶 `ErrorCode` 與可選 `message`、`userMessageKey`、`retryAfterSeconds`。
  - 建立/回傳方式：`Result.Success()`、`Result.Success(value)`、`Result.Failure(ErrorCode, ...)`。
  - 例：`return Result.Failure(ErrorCode.IAPNotInitialized, message: "IAP init timeout")`。
- 日誌：
  - 使用 `Logger` 與 `Logger.Label.CreateFromCurrentClass()` 生成分類；以 `Debug/Info/Error` 等級輸出。避免新增自定義等級名稱（如 Warning 若不存在）。
- 非同步：
  - 採 `Cysharp.Threading.Tasks` 的 `UniTask`/`UniTask<T>`；支援取消權杖 `CancellationToken`。
  - 長流程 API 應傳回 `Result`，避免直接丟例外當作流程控制。
- 設定與資料：
  - 遊戲設定透過 `IGameDataManager` 讀取（例：`GetSettingTable<GlobalSetting>()`）。
  - 與雲端同步資料走 PlayFab/Azure Functions；集中處理錯誤與重試。

## 4) 代表性範例
- `Common/Shared/Result.cs`：
  - 定義 `Error` 與 `Result` 家族，並提供靜態工廠方法。所有新服務請參考此模式回傳。
- `Common/Shared/ErrorCode.cs`：
  - 錯誤碼集中管理；如 IAP：`IAPPurchasingUnavailable (402)`、`IAPStoreConnectionLost (403)`、`IAPProductNotFound (413)` 等。
- `Common/PlayFab/PlayFabManager.cs`：
  - Azure Functions 呼叫核心，包含自動重試邏輯與 PlayFab 錯誤碼映射
  - 支援 `Token`/`LobbyToken`/`SessionTicket` 多重驗證機制
  - 事件系統：`OnRequestStart`、`OnRequestEnd`、`OnRequestError` 用於全域監控
- `Common/HttpManager/HttpManager.cs`：
  - BestHTTP 封裝，支援 GET/POST 請求與自動重試（最多5次）
  - 統一的 JSON 序列化與錯誤處理（`AsyncHTTPException` 轉 `ErrorCode`）
- `Services/FriendService/ChatService.cs`：
  - Photon Chat 整合，支援線上狀態管理與自動重連
  - 地區設定：強制使用 "EU" 區域確保全球玩家在同一服務區
  - WebGL 平台特殊處理：`UseBackgroundWorkerForSending = false`
- `Assets/ThirdParty/PlayFabQos/PlayFabQosApi.cs`：
  - UDP 區域延遲檢測核心，支援並行多區域 ping 測試（埠號 3075）
  - 智能重試與超時處理，濾除最佳/最差結果計算平均延遲
  - 配對系統整合：`MatchingService` 使用 QoS 結果選擇最佳遊戲區域
- `Assets/ThirdParty/PlayFabMultiplayer/Scripts/PlayFabAPI.cs`：
  - PlayFab Multiplayer API 統一封裝，提供 Lobby/Matchmaking 功能
  - RetryAfter 速率限制處理：自動快取與延遲重試機制
  - UniTask 整合：所有 API 回傳 `PlayFabResult<T>` 支援非同步操作
- `Authentication/AuthenticationManager.cs`：
  - 統一登入流程管理，支援 Anonymous/Google/Apple 三種登入方式
  - 平台適配：使用不同的 PlayFab 登入 API（CustomID/AndroidDeviceID/IOSDeviceID）
  - 登入快取：PlayerPrefs 儲存登入憑證，支援自動重新驗證
  - 維護模式整合：透過 `CustomPlayFabClientAPI` 檢查維護狀態

## 5) 建置 / 執行 / 測試（開發流程）
- Unity 工程：**必須使用 Unity 2021.3.44f1** 開啟根目錄（`ProjectCP.sln` 與多個 `*.csproj` 同步生成）。
- 關鍵依賴套件：
  - `UniTask 2.3.3`、`Addressables 1.19.19`、`Unity IAP 5.0.0-pre.8`
  - `Photon Fusion`（網路多人遊戲）、`PlayFab SDK`（後端服務）
  - `URP 12.1.15`（渲染管線）、`Spine-Unity`（2D 動畫）
- 編譯：依 Unity 自動編譯；如需 CI，請確保使用對應 Unity 版本與有權限拉取 UPM 套件。
- 測試執行：
  - Unity Test Runner：`Assets/Tests/EditorTests/` 包含單元測試（NUnit + Moq）
  - 執行命令：Window → General → Test Runner，選擇 EditMode 或 PlayMode
  - 測試工具類：`TestUtility` 提供常用斷言方法與容差比較
- Docker 部署（伺服器）：
  - 建置：`DeployScripts/build_image.bat` （需設定 `VERSION` 環境變數）
  - 部署：`DeployScripts/deploy_playfab.bat`（需設定 `REMOTE` 與 `VERSION`）
  - 伺服器執行檔位於 `Builds/FusionDockerImage/`，基於 Ubuntu Jammy
- 記錄：建議查看 `Logs/` 與 Console。常見問題（IAP 初始化、網路權限）已在錯誤碼中覆蓋。

## 6) IAP 與在地化注意事項（專案特有）
- Unity IAP v5（目前 `5.0.0-pre.8`）：
  - 初始化與商品同步以裝置狀態與網路為前置。IAP 事件請走專屬 Listener 與 `Result` 映射到 `ErrorCode`。
  - `Product.metadata.localizedPriceString` 依「裝置語言/地區」顯示；實際結算以「商店原生視窗」為準（Google 帳號/商店後端）。
  - 若 UI 幣別與結算幣別不同，屬預期現象；不要用 UI 幣別做伺服器邏輯判斷。
- Apple Store 實現與驗證架構：
  - 收據驗證：透過 `VerifyReceiptRequest` 發送到 PlayFab Azure Functions，包含 `IAPStoreType.AppleAppStore`、`transactionId`、`payload`
  - 錯誤處理：`IAPSignatureInvalid (422)`、`IAPVerifyReceiptFailed (440)` 等專屬錯誤碼映射
  - 待處理購買：`ContinuePendingPurchaseAsync()` 處理初始化後的未完成交易，支援重新驗證與完成流程
  - 商店連接：自動處理 `OnStoreDisconnected()` 事件，清理控制器狀態並失敗進行中的操作
  - 交易確認：使用 `ConfirmPendingPurchase()` 完成 Apple/Google 的交易確認流程
- 本地化與多語言：
  - 使用 `Physalia.Localization` 套件處理本地化
  - 錯誤訊息支援 `userMessageKey` 作為本地化索引鍵

## 7) 代碼撰寫建議（本專案特有）
- 新增服務：
  - 介面於對應子資料夾 `.../Services/` 或模組根目錄，實作類以 `Result/UniTask` 返回。
  - 所有對外呼叫（網路、IAP、存檔）以錯誤碼映射集中處理，避免在 UI 邏輯中分支過多。
- 例外與重試：
  - 優先回傳 `Result.Failure(...)`；僅對「不可恢復錯誤」拋出例外並在邊界統一攔截。
- IAP 服務設計：
  - 單一購買鎖定：`IAPManager` 同時間僅支援一個購買流程，使用 `IsProcessing()` 檢查狀態。
  - 收據結構：`PurchaseReceipt` 包含 `Store`、`TransactionID`、`Payload` 三個 JSON 欄位（大小寫敏感）。
  - 平台差異處理：Android 延遲購買（Deferred Purchase）回傳 `PurchaseProcessingResult.Pending`。
- 命名空間：統一以 `Fanimax.CP` / `Fanimax.CP.*`。

## 8) 常見落坑與提醒
- 不要直接修改第三方套件目錄（`Packages/`、`Assets/ThirdParty/`）。
- IAP API 已有新舊版本差異（v5 換新介面）；避免新增使用「過時屬性」如 `transactionID/receipt` 的新代碼。
- Logger 若無 `Warning` 等級請使用已存在的等級（`Debug/Info/Error`）。

---
如有不清楚的工作流（例如具體測試方式、打包流程或特定模組初始化順序），請留言告知，我會補齊說明與範例。


## 1) 大架構與模組邊界
- 主要代碼位於 `Assets/Scripts` 下，依功能分層：
  - `Common/`：跨域通用邏輯與基礎設施。
    - `Shared/Result.cs` 定義 Result / Result<T> 與 Error/ErrorCode，為全專案錯誤與流程控制的核心抽象；所有服務回傳都應以 `Result` 家族為主。
    - `Shared/ErrorCode.cs` 為集中枚舉，涵蓋 IAP、Ads、Matchmaking、Gameplay 等錯誤碼；請重用既有錯誤碼，避免新增重覆概念。
  - `GameData/`、`Gameplay/`、`GameplaySimulator/`、`GameplayTutorial/`：遊戲域場景與模擬。
  - `IAPService/`：內購整合（Unity IAP v5）。注意裝置地區 vs 帳號地區的貨幣顯示差異；UI 僅作為顯示參考，實際結算以商店原生視窗為準。
  - 其他資料夾（Authentication、Main、Protocols、Services、Utilities、CommonUI 等）對應子系統職責，遵守 Result/Logger 慣例。
- 網路架構（Photon Fusion）：
  - 多人遊戲使用 Fusion NetworkBehaviour，支援 Host/Client 與 Dedicated Server 模式
  - 核心網路類：`NetworkedPickupManager`、`NetworkedSwitchSystemManager`、`NetworkedTrapManager`
  - Fusion 容量限制：MaxAbilityCount/MaxStatusCount = 16，MaxTokenCount = 24（見 `GameplayConst.cs`）
  - 教學模式有獨立的 `GameplayModeTutorial` 網路實作
- 網路通訊協定與 API：
  - **PlayFab Azure Functions**：主要後端 API，透過 `PlayFabManager.Execute<T,K>()` 呼叫，支援自動重試與錯誤映射
  - **PlayFab Multiplayer API**：多人遊戲專用 API 模組（`Fanimax.PlayFabMultiplayer`）
    - Lobby 系統：`PlayFabLobbyAPI` 處理大廳創建、加入、離開與更新
    - Matchmaking 系統：`PlayFabMatchmakingAPI` 處理配對票券與區域延遲檢測
    - PubSub 即時訊息：`PlayFabPubSubManager` 基於 SignalR 的即時通訊
    - 速率限制處理：內建 RetryAfter 錯誤快取與自動重試機制
  - **Photon Chat**：即時聊天與線上狀態，使用 `ChatService` 管理連接與重連邏輯
  - **PlayFab PubSub**：即時訊息推送，用於配對、自訂房間等功能
  - **BestHTTP**：自訂 HTTP 請求，透過 `HttpManager` 封裝，支援 GET/POST 與重試機制
  - **UnityWebRequest**：基礎網路檢查（`NetworkUtility.CheckInternetConnection`）
  - **PlayFab QoS**：區域延遲檢測，透過 `PlayFabQosApi.GetQosResultAsync()` 執行 UDP ping 測試
    - 支援多區域並行延遲測試（預設 10 次 ping，250ms 超時）
    - 錯誤碼：`MatchmakingGetQosFailed (210)`、`MatchmakingGetNoValidRegionLatency (211)`
    - 用於配對系統的最佳區域選擇（`MatchingService.GetRegionLatencyWithRetryAsync()`）
  - **PlayFab 登入系統**：多平台身份驗證與帳號管理
    - 平台登入：支援 Anonymous、Google、Apple 三種登入方式
    - 帳號綁定：支援第三方帳號連結/解除連結操作
    - SessionTicket 管理：登入成功後設定 `PlayFabManager.SessionTicket`
    - 玩家資料同步：`GetPlayerDataAsync()` 取得玩家資料並設定 Token/LobbyToken
- 外部依賴（PlayFab、Photon/Fusion、Addressables、Spine 等）位於 `Packages/` 與 `Assets/ThirdParty/`；避免在這些檔案夾內直接修改第三方代碼。

## 2) 重要設計慣例
- 錯誤/流程控制：
  - 以 `Result`/`Result<T>` 傳遞成功與失敗，失敗時帶 `ErrorCode` 與可選 `message`、`userMessageKey`、`retryAfterSeconds`。
  - 建立/回傳方式：`Result.Success()`、`Result.Success(value)`、`Result.Failure(ErrorCode, ...)`。
  - 例：`return Result.Failure(ErrorCode.IAPNotInitialized, message: "IAP init timeout")`。
- 日誌：
  - 使用 `Logger` 與 `Logger.Label.CreateFromCurrentClass()` 生成分類；以 `Debug/Info/Error` 等級輸出。避免新增自定義等級名稱（如 Warning 若不存在）。
- 非同步：
  - 採 `Cysharp.Threading.Tasks` 的 `UniTask`/`UniTask<T>`；支援取消權杖 `CancellationToken`。
  - 長流程 API 應傳回 `Result`，避免直接丟例外當作流程控制。
- 設定與資料：
  - 遊戲設定透過 `IGameDataManager` 讀取（例：`GetSettingTable<GlobalSetting>()`）。
  - 與雲端同步資料走 PlayFab/Azure Functions；集中處理錯誤與重試。

## 3) 代表性範例
- `Common/Shared/Result.cs`：
  - 定義 `Error` 與 `Result` 家族，並提供靜態工廠方法。所有新服務請參考此模式回傳。
- `Common/Shared/ErrorCode.cs`：
  - 錯誤碼集中管理；如 IAP：`IAPPurchasingUnavailable (402)`、`IAPStoreConnectionLost (403)`、`IAPProductNotFound (413)` 等。
- `Common/PlayFab/PlayFabManager.cs`：
  - Azure Functions 呼叫核心，包含自動重試邏輯與 PlayFab 錯誤碼映射
  - 支援 `Token`/`LobbyToken`/`SessionTicket` 多重驗證機制
  - 事件系統：`OnRequestStart`、`OnRequestEnd`、`OnRequestError` 用於全域監控
- `Common/HttpManager/HttpManager.cs`：
  - BestHTTP 封裝，支援 GET/POST 請求與自動重試（最多5次）
  - 統一的 JSON 序列化與錯誤處理（`AsyncHTTPException` 轉 `ErrorCode`）
- `Services/FriendService/ChatService.cs`：
  - Photon Chat 整合，支援線上狀態管理與自動重連
  - 地區設定：強制使用 "EU" 區域確保全球玩家在同一服務區
  - WebGL 平台特殊處理：`UseBackgroundWorkerForSending = false`
- `Assets/ThirdParty/PlayFabQos/PlayFabQosApi.cs`：
  - UDP 區域延遲檢測核心，支援並行多區域 ping 測試（埠號 3075）
  - 智能重試與超時處理，濾除最佳/最差結果計算平均延遲
  - 配對系統整合：`MatchingService` 使用 QoS 結果選擇最佳遊戲區域
- `Assets/ThirdParty/PlayFabMultiplayer/Scripts/PlayFabAPI.cs`：
  - PlayFab Multiplayer API 統一封裝，提供 Lobby/Matchmaking 功能
  - RetryAfter 速率限制處理：自動快取與延遲重試機制
  - UniTask 整合：所有 API 回傳 `PlayFabResult<T>` 支援非同步操作
- `Authentication/AuthenticationManager.cs`：
  - 統一登入流程管理，支援 Anonymous/Google/Apple 三種登入方式
  - 平台適配：使用不同的 PlayFab 登入 API（CustomID/AndroidDeviceID/IOSDeviceID）
  - 登入快取：PlayerPrefs 儲存登入憑證，支援自動重新驗證
  - 維護模式整合：透過 `CustomPlayFabClientAPI` 檢查維護狀態

## 4) 建置 / 執行 / 測試（開發流程）
- Unity 工程：**必須使用 Unity 2021.3.44f1** 開啟根目錄（`ProjectCP.sln` 與多個 `*.csproj` 同步生成）。
- 關鍵依賴套件：
  - `UniTask 2.3.3`、`Addressables 1.19.19`、`Unity IAP 5.0.0-pre.8`
  - `Photon Fusion`（網路多人遊戲）、`PlayFab SDK`（後端服務）
  - `URP 12.1.15`（渲染管線）、`Spine-Unity`（2D 動畫）
- 編譯：依 Unity 自動編譯；如需 CI，請確保使用對應 Unity 版本與有權限拉取 UPM 套件。
- 測試執行：
  - Unity Test Runner：`Assets/Tests/EditorTests/` 包含單元測試（NUnit + Moq）
  - 執行命令：Window → General → Test Runner，選擇 EditMode 或 PlayMode
  - 測試工具類：`TestUtility` 提供常用斷言方法與容差比較
- Docker 部署（伺服器）：
  - 建置：`DeployScripts/build_image.bat` （需設定 `VERSION` 環境變數）
  - 部署：`DeployScripts/deploy_playfab.bat`（需設定 `REMOTE` 與 `VERSION`）
  - 伺服器執行檔位於 `Builds/FusionDockerImage/`，基於 Ubuntu Jammy
- 記錄：建議查看 `Logs/` 與 Console。常見問題（IAP 初始化、網路權限）已在錯誤碼中覆蓋。

## 5) IAP 與在地化注意事項（專案特有）
- Unity IAP v5（目前 `5.0.0-pre.8`）：
  - 初始化與商品同步以裝置狀態與網路為前置。IAP 事件請走專屬 Listener 與 `Result` 映射到 `ErrorCode`。
  - `Product.metadata.localizedPriceString` 依「裝置語言/地區」顯示；實際結算以「商店原生視窗」為準（Google 帳號/商店後端）。
  - 若 UI 幣別與結算幣別不同，屬預期現象；不要用 UI 幣別做伺服器邏輯判斷。
- 本地化與多語言：
  - 使用 `Physalia.Localization` 套件處理本地化
  - 錯誤訊息支援 `userMessageKey` 作為本地化索引鍵

## 6) 代碼撰寫建議（本專案特有）
- 新增服務：
  - 介面於對應子資料夾 `.../Services/` 或模組根目錄，實作類以 `Result/UniTask` 返回。
  - 所有對外呼叫（網路、IAP、存檔）以錯誤碼映射集中處理，避免在 UI 邏輯中分支過多。
- 例外與重試：
  - 優先回傳 `Result.Failure(...)`；僅對「不可恢復錯誤」拋出例外並在邊界統一攔截。
- IAP 服務設計：
  - 單一購買鎖定：`IAPManager` 同時間僅支援一個購買流程，使用 `IsProcessing()` 檢查狀態。
  - 收據結構：`PurchaseReceipt` 包含 `Store`、`TransactionID`、`Payload` 三個 JSON 欄位（大小寫敏感）。
  - 平台差異處理：Android 延遲購買（Deferred Purchase）回傳 `PurchaseProcessingResult.Pending`。
- 命名空間：統一以 `Fanimax.CP` / `Fanimax.CP.*`。

## 7) 常見落坑與提醒
- 不要直接修改第三方套件目錄（`Packages/`、`Assets/ThirdParty/`）。
- IAP API 已有新舊版本差異（v5 換新介面）；避免新增使用「過時屬性」如 `transactionID/receipt` 的新代碼。
- Logger 若無 `Warning` 等級請使用已存在的等級（`Debug/Info/Error`）。

---
如有不清楚的工作流（例如具體測試方式、打包流程或特定模組初始化順序），請留言告知，我會補齊說明與範例。
