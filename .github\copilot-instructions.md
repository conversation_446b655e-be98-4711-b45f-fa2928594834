# ProjectCP – AI 協作者說明（Copilot Instructions）

本文件為 AI 程式碼協作者提供速讀手冊，聚焦「本專案特有的結構、流程與慣例」。請在開始大型變更前先掃描此文與註解中的範例檔案。

## 1) 核心開發理念與工作流程 (非常重要！)
本專案採用**資料驅動設計 (Data-Driven Design)**。這是最關鍵的、必須遵守的開發模式。

- **原則**: **切勿在程式碼中寫死任何遊戲數值、設定或文字內容。** 所有可配置的資料都源於 `GameData/` 目錄下的 Excel 檔案。C# 腳本的職責是讀取這些資料並實現邏輯，而不是儲存資料。

- **關鍵工作流程：資料匯出**:
  1. **修改 Excel**: 在 `GameData/` 中找到對應的 `.xlsx` 或 `.xlsm` 檔案進行編輯。
  2. **執行匯出工具**: 執行 `OperationConsole/ProjectCP.Operation.exe`。此工具會讀取 Excel 檔案並生成 Unity 引擎可用的資料資產 (例如 ScriptableObjects)。
  3. **驗證**: 執行後，相關的資料資產會被更新，通常位於 `Assets/GameResources/GameData/`。遊戲透過 `IGameDataManager` 讀取這些資產。

**範例**: 若要調整角色能力，你必須去修改 `GameData/AbilityData.xlsm`，然後執行匯出工具，而不是直接在 C# 程式碼中尋找並修改數值。

---

## 2) 大架構與模組邊界
- **主要代碼位於 `Assets/Scripts` 下，依功能分層**：
  - `Common/`：跨域通用邏輯與基礎設施
    - `Shared/Result.cs` 定義 Result/Result<T> 與 Error/ErrorCode，為全專案錯誤與流程控制的核心抽象
    - `Shared/ErrorCode.cs` 為集中枚舉，涵蓋 IAP、Ads、Matchmaking、Gameplay 等錯誤碼
    - `PlayFab/PlayFabManager.cs` 處理所有 Azure Functions 呼叫，支援自動重試與錯誤映射
  - `GameData/`：資料驅動系統的程式碼生成目錄，包含從 Excel 自動生成的資料類別
  - `Gameplay/`、`GameplaySimulator/`、`GameplayTutorial/`：遊戲核心邏輯與模擬
  - `IAPService/`：內購整合（Unity IAP v5），注意裝置地區 vs 帳號地區的貨幣顯示差異
  - `Authentication/`：多平台登入系統（Anonymous、Google、Apple）
  - `Services/`、`Utilities/`、`CommonUI/`：各子系統服務與工具

- **網路架構（Photon Fusion）**：
  - 多人遊戲使用 Fusion NetworkBehaviour，支援 Host/Client 與 Dedicated Server 模式
  - 核心網路類：`NetworkedPickupManager`、`NetworkedSwitchSystemManager`、`NetworkedTrapManager`
  - Fusion 容量限制：MaxAbilityCount/MaxStatusCount = 16，MaxTokenCount = 24（見 `GameplayConst.cs`）

- **服務定位器模式**：
  - 使用 `ServiceLocator` 管理依賴注入，在 `MainContext.cs` 中註冊所有服務
  - 主要服務：`IGameDataManager`、`IPlayFabManager`、`IAssetManager`、`ILocalization` 等

## 3) 重要設計慣例
- **錯誤/流程控制**：
  - 以 `Result`/`Result<T>` 傳遞成功與失敗，失敗時帶 `ErrorCode` 與可選參數
  - 建立方式：`Result.Success()`、`Result.Success(value)`、`Result.Failure(ErrorCode, ...)`
  - 範例：`return Result.Failure(ErrorCode.IAPNotInitialized, message: "IAP init timeout")`

- **非同步處理**：
  - 採用 `Cysharp.Threading.Tasks` 的 `UniTask`/`UniTask<T>`
  - 支援取消權杖 `CancellationToken`
  - 長流程 API 應傳回 `Result`，避免直接丟例外當作流程控制

- **日誌系統**：
  - 使用 `Logger` 與 `Logger.Label.CreateFromCurrentClass()` 生成分類
  - 以 `Debug/Info/Error` 等級輸出，避免新增自定義等級名稱

- **資料存取**：
  - 遊戲設定透過 `IGameDataManager` 讀取（例：`GetSettingTable<GlobalSetting>()`）
  - 與雲端同步資料走 PlayFab/Azure Functions，集中處理錯誤與重試

## 4) 技術棧與依賴
- **Unity 版本**: 必須使用 Unity 2021.3.44f1
- **關鍵套件**：
  - `UniTask 2.3.3`：非同步處理
  - `Addressables 1.19.19`：資源管理
  - `Unity IAP 5.0.0-pre.8`：內購系統
  - `URP 12.1.15`：渲染管線
  - `Photon Fusion`：網路多人遊戲
  - `PlayFab SDK`：後端服務
  - `Spine-Unity`：2D 動畫
  - `Physalia.ExcelDataExporter`：Excel 資料匯出工具
  - `Physalia.Localization`：本地化系統

## 5) 代表性範例檔案
- `Common/Shared/Result.cs`：錯誤處理與流程控制的核心抽象
- `Common/PlayFab/PlayFabManager.cs`：Azure Functions 呼叫核心，包含重試邏輯
- `Authentication/AuthenticationManager.cs`：統一登入流程管理
- `IAPService/IAPManager.cs`：內購系統實作，支援收據驗證
- `Utilities/ServiceLocator.cs`：依賴注入容器
- `Main/MainContext.cs`：服務註冊與初始化

## 6) 開發工作流程
- **資料修改流程**：修改 `GameData/*.xlsx` → 執行 `OperationConsole/ProjectCP.Operation.exe` → 驗證生成的 `Assets/GameResources/GameData/` 資產
- **測試執行**：使用 Unity Test Runner，測試位於 `Assets/Tests/EditorTests/`
- **建置部署**：使用 `DeployScripts/` 下的批次檔案進行 Docker 建置與部署

## 7) 專案特有注意事項
- **IAP 系統**：UI 顯示的價格僅供參考，實際結算以商店原生視窗為準
- **網路通訊**：所有 API 呼叫都透過 `PlayFabManager` 統一處理，支援自動重試與錯誤映射
- **命名空間**：統一使用 `Fanimax.CP` / `Fanimax.CP.*`
- **第三方套件**：避免直接修改 `Packages/` 與 `Assets/ThirdParty/` 內的代碼

## 8) 常見陷阱
- 不要在程式碼中寫死遊戲數值，必須使用資料驅動設計
- 不要直接修改第三方套件目錄
- Logger 若無 `Warning` 等級請使用已存在的等級（`Debug/Info/Error`）
- IAP API 使用 v5 新介面，避免使用過時屬性

---
如有不清楚的工作流程或特定模組實作細節，請參考上述範例檔案或詢問補充說明。
