%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45f4116f2cdfba14b8cf00f26426a3d4, type: 3}
  m_Name: Ability_200010002
  m_EditorClassIdentifier: 
  blackboard:
  - key: speed
    value: 0
  - key: rangeX
    value: 0
  - key: lifetime
    value: 0
  - key: stun
    value: 0
  - key: energyRecovery
    value: 0
  - key: test
    value: 0
  graphGroups:
  - graphs:
    - nodes:
      - rid: 8923418968427856053
      - rid: 8923418968427856054
      - rid: 8923418968427856055
      - rid: 8923418968427856056
      - rid: 8923418968427856057
      - rid: 8923418968427856058
      - rid: 8923418968427856059
      - rid: 8923418968427856060
      - rid: 8923418968427856061
      - rid: 8923418968427856062
      - rid: 8923418968427856063
      edges:
      - id1: 770219
        port1: next
        id2: 63208
        port2: previous
      - id1: 45164
        port1: value
        id2: 753509
        port2: a
      - id1: 15975
        port1: character
        id2: 63208
        port2: targets
      - id1: 63208
        port1: next
        id2: 664075
        port2: previous
      - id1: 664075
        port1: next
        id2: 689082
        port2: previous
      - id1: 275682
        port1: character
        id2: 664075
        port2: targets
      - id1: 228145
        port1: character
        id2: 770219
        port2: targets
      - id1: 417498
        port1: next
        id2: 770219
        port2: previous
      - id1: 689082
        port1: next
        id2: 833722
        port2: previous
      - id1: 753509
        port1: result
        id2: 770219
        port2: ticks
  - graphs:
    - nodes:
      - rid: 8923418968427856064
      - rid: 8923418968427856065
      - rid: 8923418968427856066
      - rid: 8923418968427856067
      - rid: 8923418968427856068
      - rid: 8923418968427856069
      - rid: 8923418968427856070
      - rid: 8923418968427856071
      - rid: 8923418968427856072
      - rid: 8923418968427856073
      - rid: 8923418968427856074
      - rid: 8923418968427856075
      - rid: 8923418968427856076
      - rid: 8923418968427856077
      edges:
      - id1: 377778
        port1: value
        id2: 584857
        port2: lifetime
      - id1: 584857
        port1: next
        id2: 751853
        port2: previous
      - id1: 248604
        port1: value
        id2: 584857
        port2: size2
      - id1: 212962
        port1: value
        id2: 584857
        port2: size
      - id1: 676238
        port1: character
        id2: 584857
        port2: caster
      - id1: 676238
        port1: character
        id2: 584857
        port2: anchor
      - id1: 260465
        port1: next
        id2: 584857
        port2: previous
      - id1: 862901
        port1: value
        id2: 260465
        port2: ticks
      - id1: 870235
        port1: next
        id2: 260465
        port2: previous
      - id1: 253729
        port1: value
        id2: 584857
        port2: stunTicks
      - id1: 433
        port1: value
        id2: 584857
        port2: energyGainForHit
      - id1: 332432
        port1: character
        id2: 239630
        port2: targets
      - id1: 56667
        port1: next
        id2: 239630
        port2: previous
  references:
    version: 2
    RefIds:
    - rid: 8923418968427856053
      type: {class: AppendAbilityNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 770219
        gameplayAbilityId: -1
        groupIndex: 1
        ticks: 0
        param0: 0
        param1: 0
        param2: 0
        param3: 0
        overwrite: 0
        ignoreClearWhenStunned: 0
        showDurationHUD: 0
    - rid: 8923418968427856054
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 45164
        key: 90
    - rid: 8923418968427856055
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 15975
    - rid: 8923418968427856056
      type: {class: AccelerateToMaxNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 63208
    - rid: 8923418968427856057
      type: {class: FinishNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 833722
    - rid: 8923418968427856058
      type: {class: PlayVfxNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 664075
        ownerOnly: 0
        vfxName: Vfx_004_Lonzo_00_CyberItem_UpperCut_Start
        sfxName: 
        degree: 0
        boneType: 1
        customBoneId: 0
        offsetX: 1.75
        offsetY: 3.125
        isInWorld: 0
    - rid: 8923418968427856059
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 275682
    - rid: 8923418968427856060
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 228145
    - rid: 8923418968427856061
      type: {class: OnActivatedData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 417498
        ignoreStun: 0
    - rid: 8923418968427856062
      type: {class: CueCVNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 689082
        abilityCVId: -2
    - rid: 8923418968427856063
      type: {class: IntAddNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 753509
        a: 0
        b: 1
    - rid: 8923418968427856064
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 377778
        key: lifetime
    - rid: 8923418968427856065
      type: {class: CreateAreaNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 584857
        size: 0
        size2: 0
        rotation: 0
        offsetX: 0
        offsetY: 0
        speed: 0
        faceDirection: 0
        lifetime: 0
        deactivationTime: 15
        stunTicks: 0
        energyGainForHit: 0
        canBreakProjectile: 1
        hitPeriodTick: 0
        gameplayAbilityId: -1
        groupIndex: 3
        vfxDataId: 30071001
        hitFxDataId: 3000100
        discardPrevious: 0
        triggerPreviousFinished: 0
        lockX: 0
        lockY: 0
        canHitLevelTrap: 0
        canBreakLevelTrap: 0
        ignoreHitIfPreviousHit: 0
        shapeType: 1
        pathType: 9
        rootType: 0
        pathArg1: 0
        pathArg2: 0
        pathArg3: 0
    - rid: 8923418968427856066
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 248604
        key: rangeY
    - rid: 8923418968427856067
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 212962
        key: rangeX
    - rid: 8923418968427856068
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 676238
    - rid: 8923418968427856069
      type: {class: DelayNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 260465
        ticks: 0
        nextActionTickSlot: 1
        countSlot: 2
    - rid: 8923418968427856070
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 862901
        key: 90
    - rid: 8923418968427856071
      type: {class: OnTickNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 870235
        ignoreStun: 0
        ignoreKnockedDown: 0
    - rid: 8923418968427856072
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 253729
        key: stun
    - rid: 8923418968427856073
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 433
        key: energyRecovery
    - rid: 8923418968427856074
      type: {class: PlayVfxNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 239630
        ownerOnly: 0
        vfxName: Vfx_005_Kaiso_00_Passive_Debuff
        sfxName: 
        degree: 0
        boneType: 0
        customBoneId: 0
        offsetX: 0
        offsetY: 0
        isInWorld: 0
    - rid: 8923418968427856075
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 332432
    - rid: 8923418968427856076
      type: {class: OnTickNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 56667
        ignoreStun: 0
        ignoreKnockedDown: 0
    - rid: 8923418968427856077
      type: {class: LogNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 751853
        text: "\u65B9\u7A0B\u5F0F"
