%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45f4116f2cdfba14b8cf00f26426a3d4, type: 3}
  m_Name: Ability_3001200
  m_EditorClassIdentifier: 
  blackboard:
  - key: lifetime
    value: 0
  - key: rangeX
    value: 0
  - key: rangeY
    value: 0
  - key: invulnerable
    value: 0
  - key: stun
    value: 0
  - key: energyRecovery
    value: 0
  - key: cooldown
    value: 0
  - key: offsetX
    value: 0
  - key: delay
    value: 0
  - key: rangeX0
    value: 0
  graphGroups:
  - graphs:
    - nodes:
      - rid: 8923418986610950144
      - rid: 8923418986610950145
      - rid: 8923418986610950146
      - rid: 8923418986610950147
      - rid: 8923418986610950148
      - rid: 8923418986610950149
      - rid: 8923418986610950150
      - rid: 8923418986610950151
      - rid: 8923418986610950152
      - rid: 8923418986610950153
      - rid: 8923418986610950154
      - rid: 8923418986610950155
      - rid: 8923418986610950156
      - rid: 8923418986610950157
      - rid: 8923418986610950158
      - rid: 8923418986610950159
      - rid: 8923418986610950160
      - rid: 8923418986610950161
      - rid: 8923418986610950162
      - rid: 8923418986610950163
      - rid: 8923418986610950164
      - rid: 8923418986610950165
      - rid: 8923418986610950166
      - rid: 8923418986610950167
      - rid: 8923418986610950168
      - rid: 8923418986610950169
      - rid: 8923418986610950170
      - rid: 8923418986610950171
      - rid: 8923418986610950172
      - rid: 8923418986610950173
      - rid: 8923418986610950174
      - rid: 8923418986610950175
      - rid: 8923418986610950176
      - rid: 8923418986610950177
      - rid: 8923418986610950178
      - rid: 8923418986610950179
      - rid: 8923418986610950180
      - rid: 8923418986610950181
      - rid: 8923418986610950182
      - rid: 8923418986610950183
      - rid: 8923418986610950184
      - rid: 8923418986610950185
      - rid: 8923418986610950186
      - rid: 8923418986610950187
      - rid: 8923418986610950188
      - rid: 8923418986610950189
      - rid: 8923418986610950190
      - rid: 8923418986610950191
      - rid: 8923418986610950192
      - rid: 8923418986610950193
      - rid: 8923418986610950194
      - rid: 8923418986610950195
      - rid: 8923418986610950196
      - rid: 8923418986610950197
      - rid: 8923418986610950198
      - rid: 8923418986610950199
      - rid: 8923418986610950200
      - rid: 8923418986610950201
      - rid: 8923418986610950202
      - rid: 8923418986610950203
      - rid: 8923418986610950204
      - rid: 8923418986610950205
      - rid: 8923418986610950206
      - rid: 8923418986610950207
      - rid: 8923418986610950208
      - rid: 8923418986610950209
      - rid: 8923418986610950210
      - rid: 8923418986610950211
      - rid: 8923418986610950212
      edges:
      - id1: 448877
        port1: next
        id2: 195781
        port2: previous
      - id1: 248742
        port1: next
        id2: 954640
        port2: previous
      - id1: 248742
        port1: self
        id2: 954640
        port2: targets
      - id1: 394041
        port1: next
        id2: 596396
        port2: previous
      - id1: 209187
        port1: character
        id2: 394041
        port2: targets
      - id1: 296641
        port1: character
        id2: 704526
        port2: caster
      - id1: 296641
        port1: character
        id2: 704526
        port2: anchor
      - id1: 728522
        port1: value
        id2: 704526
        port2: stunTicks
      - id1: 307993
        port1: value
        id2: 704526
        port2: energyGainForHit
      - id1: 596396
        port1: next
        id2: 820407
        port2: previous
      - id1: 364763
        port1: character
        id2: 596396
        port2: targets
      - id1: 215785
        port1: character
        id2: 559972
        port2: targets
      - id1: 559972
        port1: next
        id2: 550377
        port2: previous
      - id1: 704526
        port1: next
        id2: 559972
        port2: previous
      - id1: 28981
        port1: value
        id2: 704526
        port2: size2
      - id1: 424120
        port1: value
        id2: 704526
        port2: lifetime
      - id1: 550377
        port1: next
        id2: 394041
        port2: previous
      - id1: 195781
        port1: next
        id2: 326637
        port2: previous
      - id1: 38838
        port1: character
        id2: 195781
        port2: targets
      - id1: 740545
        port1: value
        id2: 195781
        port2: ticks
      - id1: 326637
        port1: next
        id2: 890686
        port2: previousPort
      - id1: 326637
        port1: originCenter
        id2: 988056
        port2: a
      - id1: 326637
        port1: originCenter
        id2: 292260
        port2: a
      - id1: 326637
        port1: originCenter
        id2: 412247
        port2: value
      - id1: 326637
        port1: resultCenter
        id2: 988056
        port2: b
      - id1: 326637
        port1: resultCenter
        id2: 292260
        port2: b
      - id1: 326637
        port1: resultCenter
        id2: 935746
        port2: value
      - id1: 740105
        port1: value
        id2: 326637
        port2: offsetX
      - id1: 587372
        port1: character
        id2: 326637
        port2: target
      - id1: 988056
        port1: result
        id2: 451529
        port2: source
      - id1: 988056
        port1: result
        id2: 117754
        port2: source
      - id1: 988056
        port1: result
        id2: 630274
        port2: value
      - id1: 451529
        port1: next
        id2: 117754
        port2: previous
      - id1: 292260
        port1: result
        id2: 216539
        port2: vector
      - id1: 292260
        port1: result
        id2: 615994
        port2: vector
      - id1: 216539
        port1: directionInDegree
        id2: 704526
        port2: rotation
      - id1: 216539
        port1: directionInDegree
        id2: 570515
        port2: angle
      - id1: 216539
        port1: directionInDegree
        id2: 638116
        port2: a
      - id1: 638116
        port1: result
        id2: 451529
        port2: degree
      - id1: 638116
        port1: result
        id2: 117754
        port2: degree
      - id1: 615994
        port1: magnitude
        id2: 637916
        port2: value
      - id1: 615994
        port1: magnitude
        id2: 637916
        port2: max
      - id1: 890686
        port1: truePort
        id2: 644379
        port2: previous
      - id1: 890686
        port1: falsePort
        id2: 510086
        port2: previous
      - id1: 819546
        port1: value
        id2: 407106
        port2: b
      - id1: 407106
        port1: result
        id2: 117754
        port2: scaleX
      - id1: 117754
        port1: next
        id2: 704526
        port2: previous
      - id1: 637916
        port1: result
        id2: 704526
        port2: size
      - id1: 637916
        port1: result
        id2: 407106
        port2: a
      - id1: 637916
        port1: result
        id2: 570515
        port2: width
      - id1: 686009
        port1: result
        id2: 704526
        port2: offsetX
      - id1: 935746
        port1: x
        id2: 686009
        port2: b
      - id1: 935746
        port1: y
        id2: 222448
        port2: b
      - id1: 412247
        port1: x
        id2: 686009
        port2: a
      - id1: 412247
        port1: y
        id2: 222448
        port2: a
      - id1: 222448
        port1: result
        id2: 704526
        port2: offsetY
      - id1: 644379
        port1: next
        id2: 451529
        port2: previous
      - id1: 510086
        port1: next
        id2: 117754
        port2: previous
      - id1: 661463
        port1: character
        id2: 644379
        port2: targets
      - id1: 224309
        port1: character
        id2: 510086
        port2: targets
      - id1: 570515
        port1: characters
        id2: 691905
        port2: collectionPort
      - id1: 691905
        port1: countPort
        id2: 70093
        port2: a
      - id1: 70093
        port1: result
        id2: 890686
        port2: conditionPort
      - id1: 438419
        port1: character
        id2: 570515
        port2: source
      - id1: 897766
        port1: value
        id2: 570515
        port2: height
      - id1: 994547
        port1: center
        id2: 764999
        port2: value
      - id1: 630274
        port1: x
        id2: 963243
        port2: a
      - id1: 630274
        port1: y
        id2: 239102
        port2: a
      - id1: 764999
        port1: x
        id2: 963243
        port2: b
      - id1: 764999
        port1: y
        id2: 239102
        port2: b
      - id1: 239102
        port1: result
        id2: 570515
        port2: offsetY
      - id1: 963243
        port1: result
        id2: 570515
        port2: offsetX
      - id1: 869753
        port1: character
        id2: 994547
        port2: target
      - id1: 211602
        port1: character
        id2: 820407
        port2: targets
      - id1: 820407
        port1: next
        id2: 782853
        port2: previous
  - graphs:
    - nodes:
      - rid: 8923418986610950213
      - rid: 8923418986610950214
      edges:
      - id1: 246882
        port1: next
        id2: 203326
        port2: previous
  - graphs:
    - nodes:
      - rid: 8923418986610950215
      - rid: 8923418986610950216
      edges:
      - id1: 974112
        port1: next
        id2: 779560
        port2: previous
  - graphs:
    - nodes:
      - rid: 8923418986610950217
      - rid: 8923418986610950218
      - rid: 8923418986610950219
      - rid: 8923418986610950220
      - rid: 8923418986610950221
      - rid: 8923418986610950222
      - rid: 8923418986610950223
      - rid: 8923418986610950224
      - rid: 8923418986610950225
      edges:
      - id1: 69427
        port1: next
        id2: 61813
        port2: previous
      - id1: 210833
        port1: value
        id2: 69427
        port2: cooldownTicks
      - id1: 162131
        port1: next
        id2: 69427
        port2: previous
      - id1: 162131
        port1: instigator
        id2: 69427
        port2: target
      - id1: 162131
        port1: instigator
        id2: 645136
        port2: instigator
      - id1: 162131
        port1: instigator
        id2: 921370
        port2: target
      - id1: 162131
        port1: target
        id2: 645136
        port2: targets
      - id1: 293069
        port1: next
        id2: 825310
        port2: previous
      - id1: 293069
        port1: caster
        id2: 825310
        port2: instigator
      - id1: 921370
        port1: gameplayAbilityId
        id2: 69427
        port2: gameplayAbilityId
      - id1: 921370
        port1: gameplayAbilityId
        id2: 61813
        port2: text
      - id1: 61813
        port1: next
        id2: 645136
        port2: previous
  - graphs:
    - nodes:
      - rid: 8923418986610950226
      - rid: 8923418986610950227
      - rid: 8923418986610950228
      - rid: 8923418986610950229
      - rid: 8923418986610950230
      - rid: 8923418986610950231
      - rid: 8923418986610950232
      - rid: 8923418986610950233
      - rid: 8923418986610950234
      - rid: 8923418986610950235
      - rid: 8923418986610950236
      - rid: 8923418986610950237
      - rid: 8923418986610950238
      - rid: 8923418986610950239
      - rid: 8923418986610950240
      - rid: 8923418986610950241
      - rid: 8923418986610950242
      - rid: 8923418986610950243
      - rid: 8923418986610950244
      - rid: 8923418986610950245
      - rid: 8923418986610950246
      - rid: 8923418986610950247
      - rid: 8923418986610950248
      - rid: 8923418986610950249
      - rid: 8923418986610950250
      - rid: 8923418986610950251
      - rid: 8923418986610950252
      - rid: 8923418986610950253
      - rid: 8923418986610950254
      - rid: 8923418986610950255
      - rid: 8923418986610950256
      - rid: 8923418986610950257
      - rid: 8923418986610950258
      - rid: 8923418986610950259
      - rid: 8923418986610950260
      - rid: 8923418986610950261
      - rid: 8923418986610950262
      - rid: 8923418986610950263
      - rid: 8923418986610950264
      - rid: 8923418986610950265
      - rid: 8923418986610950266
      - rid: 8923418986610950267
      - rid: 8923418986610950268
      - rid: 8923418986610950269
      - rid: 8923418986610950270
      - rid: 8923418986610950271
      - rid: 8923418986610950272
      - rid: 8923418986610950273
      - rid: 8923418986610950274
      - rid: 8923418986610950275
      - rid: 8923418986610950276
      - rid: 8923418986610950277
      - rid: 8923418986610950278
      - rid: 8923418986610950279
      - rid: 8923418986610950280
      - rid: 8923418986610950281
      - rid: 8923418986610950282
      - rid: 8923418986610950283
      - rid: 8923418986610950284
      - rid: 8923418986610950285
      - rid: 8923418986610950286
      - rid: 8923418986610950287
      - rid: 8923418986610950288
      - rid: 8923418986610950289
      - rid: 8923418986610950290
      edges:
      - id1: 10404
        port1: output
        id2: 69725
        port2: text
      - id1: 432049
        port1: next
        id2: 531595
        port2: previous
      - id1: 139555
        port1: character
        id2: 432049
        port2: targets
      - id1: 139555
        port1: character
        id2: 531595
        port2: targets
      - id1: 214743
        port1: next
        id2: 432049
        port2: previous
      - id1: 753455
        port1: character
        id2: 214743
        port2: targets
      - id1: 211818
        port1: character
        id2: 433528
        port2: targets
      - id1: 433528
        port1: next
        id2: 880502
        port2: previous
      - id1: 688042
        port1: next
        id2: 433528
        port2: previous
      - id1: 880502
        port1: next
        id2: 214743
        port2: previous
      - id1: 680401
        port1: character
        id2: 684069
        port2: targets
      - id1: 229372
        port1: value
        id2: 684069
        port2: ticks
      - id1: 861585
        port1: character
        id2: 688042
        port2: caster
      - id1: 861585
        port1: character
        id2: 688042
        port2: anchor
      - id1: 947673
        port1: value
        id2: 688042
        port2: stunTicks
      - id1: 114151
        port1: value
        id2: 688042
        port2: energyGainForHit
      - id1: 142363
        port1: value
        id2: 688042
        port2: size2
      - id1: 303200
        port1: value
        id2: 688042
        port2: lifetime
      - id1: 684069
        port1: next
        id2: 468498
        port2: previous
      - id1: 468498
        port1: next
        id2: 681026
        port2: previousPort
      - id1: 468498
        port1: originCenter
        id2: 433755
        port2: a
      - id1: 468498
        port1: originCenter
        id2: 382587
        port2: a
      - id1: 468498
        port1: originCenter
        id2: 661649
        port2: value
      - id1: 468498
        port1: resultCenter
        id2: 433755
        port2: b
      - id1: 468498
        port1: resultCenter
        id2: 382587
        port2: b
      - id1: 468498
        port1: resultCenter
        id2: 596821
        port2: value
      - id1: 103789
        port1: value
        id2: 468498
        port2: offsetX
      - id1: 881662
        port1: character
        id2: 468498
        port2: target
      - id1: 433755
        port1: result
        id2: 223808
        port2: source
      - id1: 433755
        port1: result
        id2: 173347
        port2: source
      - id1: 433755
        port1: result
        id2: 598809
        port2: value
      - id1: 223808
        port1: next
        id2: 173347
        port2: previous
      - id1: 382587
        port1: result
        id2: 125611
        port2: vector
      - id1: 382587
        port1: result
        id2: 114418
        port2: vector
      - id1: 125611
        port1: directionInDegree
        id2: 688042
        port2: rotation
      - id1: 125611
        port1: directionInDegree
        id2: 617363
        port2: angle
      - id1: 125611
        port1: directionInDegree
        id2: 267142
        port2: a
      - id1: 267142
        port1: result
        id2: 223808
        port2: degree
      - id1: 267142
        port1: result
        id2: 173347
        port2: degree
      - id1: 114418
        port1: magnitude
        id2: 765106
        port2: value
      - id1: 114418
        port1: magnitude
        id2: 765106
        port2: max
      - id1: 681026
        port1: truePort
        id2: 858876
        port2: previous
      - id1: 681026
        port1: falsePort
        id2: 494892
        port2: previous
      - id1: 635257
        port1: value
        id2: 290804
        port2: b
      - id1: 290804
        port1: result
        id2: 173347
        port2: scaleX
      - id1: 173347
        port1: next
        id2: 688042
        port2: previous
      - id1: 765106
        port1: result
        id2: 688042
        port2: size
      - id1: 765106
        port1: result
        id2: 617363
        port2: width
      - id1: 765106
        port1: result
        id2: 290804
        port2: a
      - id1: 306494
        port1: result
        id2: 688042
        port2: offsetX
      - id1: 596821
        port1: x
        id2: 306494
        port2: b
      - id1: 596821
        port1: y
        id2: 458012
        port2: b
      - id1: 661649
        port1: x
        id2: 306494
        port2: a
      - id1: 661649
        port1: y
        id2: 458012
        port2: a
      - id1: 458012
        port1: result
        id2: 688042
        port2: offsetY
      - id1: 858876
        port1: next
        id2: 223808
        port2: previous
      - id1: 494892
        port1: next
        id2: 173347
        port2: previous
      - id1: 264166
        port1: character
        id2: 858876
        port2: targets
      - id1: 493074
        port1: character
        id2: 494892
        port2: targets
      - id1: 617363
        port1: characters
        id2: 819354
        port2: collectionPort
      - id1: 819354
        port1: countPort
        id2: 312039
        port2: a
      - id1: 312039
        port1: result
        id2: 681026
        port2: conditionPort
      - id1: 726775
        port1: character
        id2: 617363
        port2: source
      - id1: 834685
        port1: value
        id2: 617363
        port2: height
      - id1: 237163
        port1: center
        id2: 933724
        port2: value
      - id1: 598809
        port1: x
        id2: 162039
        port2: a
      - id1: 598809
        port1: y
        id2: 912583
        port2: a
      - id1: 933724
        port1: x
        id2: 162039
        port2: b
      - id1: 933724
        port1: y
        id2: 912583
        port2: b
      - id1: 912583
        port1: result
        id2: 617363
        port2: offsetY
      - id1: 162039
        port1: result
        id2: 617363
        port2: offsetX
      - id1: 941799
        port1: character
        id2: 237163
        port2: target
      - id1: 439693
        port1: next
        id2: 771563
        port2: previous
      - id1: 439693
        port1: self
        id2: 771563
        port2: targets
      - id1: 36262
        port1: next
        id2: 69725
        port2: previous
      - id1: 179257
        port1: next
        id2: 684069
        port2: previous
      - id1: 977079
        port1: value
        id2: 179257
        port2: ticks
      - id1: 69725
        port1: next
        id2: 179257
        port2: previous
      - id1: 441190
        port1: cooldownTicks
        id2: 171083
        port2: a
      - id1: 171083
        port1: result
        id2: 766002
        port2: cooldownTicks
  references:
    version: 2
    RefIds:
    - rid: 8923418986610950144
      type: {class: OnActivatedData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 448877
        ignoreStun: 0
    - rid: 8923418986610950145
      type: {class: FinishNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 782853
    - rid: 8923418986610950146
      type: {class: OnActivatingData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 248742
        ignoreStun: 0
    - rid: 8923418986610950147
      type: {class: AppendAbilityNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 954640
        gameplayAbilityId: -1
        groupIndex: 1
        ticks: 0
        param0: 0
        param1: 0
        param2: 0
        param3: 0
        overwrite: 0
        ignoreClearWhenStunned: 0
        showDurationHUD: 0
    - rid: 8923418986610950148
      type: {class: RemoveAbilityNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 394041
        gameplayAbilityId: -1
        groupIndex: 1
    - rid: 8923418986610950149
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 209187
    - rid: 8923418986610950150
      type: {class: StringNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 238501
        text: "rangeX=5, \u539F\u578B\u7D50\u679CDistance0~5.5\u90FD\u6703\u4E2D"
    - rid: 8923418986610950151
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 296641
    - rid: 8923418986610950152
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 728522
        key: stun
    - rid: 8923418986610950153
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 307993
        key: energyRecovery
    - rid: 8923418986610950154
      type: {class: AddCameraBumpByActorNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 596396
        ownerOnly: 1
        bumpId: 3001100
    - rid: 8923418986610950155
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 364763
    - rid: 8923418986610950156
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 215785
    - rid: 8923418986610950157
      type: {class: PlayVfxNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 559972
        ownerOnly: 0
        vfxName: Vfx_001_Park_00_CpAbility_Trail
        sfxName: 
        degree: 0
        boneType: 0
        customBoneId: 0
        offsetX: 0
        offsetY: 0
        isInWorld: 0
    - rid: 8923418986610950158
      type: {class: CreateAreaNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 704526
        size: 0
        size2: 0
        rotation: 0
        offsetX: 0
        offsetY: 0
        speed: 0
        faceDirection: 0
        lifetime: 0
        deactivationTime: 0
        stunTicks: 0
        energyGainForHit: 0
        canBreakProjectile: 1
        hitPeriodTick: 0
        gameplayAbilityId: -1
        groupIndex: 3
        vfxDataId: 0
        hitFxDataId: 0
        discardPrevious: 0
        triggerPreviousFinished: 0
        lockX: 0
        lockY: 0
        canHitLevelTrap: 0
        canBreakLevelTrap: 0
        ignoreHitIfPreviousHit: 0
        shapeType: 2
        pathType: 0
        rootType: 1
        pathArg1: 0
        pathArg2: 0
        pathArg3: 0
    - rid: 8923418986610950159
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 28981
        key: rangeY
    - rid: 8923418986610950160
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 424120
        key: lifetime
    - rid: 8923418986610950161
      type: {class: CueCVNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 550377
        abilityCVId: 3001100
    - rid: 8923418986610950162
      type: {class: AppendAbilityNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 195781
        gameplayAbilityId: -1
        groupIndex: 2
        ticks: 0
        param0: 0
        param1: 0
        param2: 0
        param3: 0
        overwrite: 1
        ignoreClearWhenStunned: 0
        showDurationHUD: 0
    - rid: 8923418986610950163
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 38838
    - rid: 8923418986610950164
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 740545
        key: invulnerable
    - rid: 8923418986610950165
      type: {class: TeleportNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 326637
        offsetX: 0
        offsetY: 0
        ignorePickup: 0
    - rid: 8923418986610950166
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 740105
        key: rangeX
    - rid: 8923418986610950167
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 587372
    - rid: 8923418986610950168
      type: {class: GetMidpointNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 988056
        a: {x: 0, y: 0}
        b: {x: 0, y: 0}
    - rid: 8923418986610950169
      type: {class: PlayVfxInWorldPositionNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 451529
        ownerOnly: 0
        source: {x: 0, y: 0}
        vfxName: Vfx_001_Park_00_CpAbility_CharHit
        sfxName: 
        degree: 0
    - rid: 8923418986610950170
      type: {class: GetVectorNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 292260
        a: {x: 0, y: 0}
        b: {x: 0, y: 0}
    - rid: 8923418986610950171
      type: {class: GetDirectionInDegreeNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 216539
        vector: {x: 0, y: 0}
    - rid: 8923418986610950172
      type: {class: FloatDivideNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 638116
        a: 0
        b: 10000
    - rid: 8923418986610950173
      type: {class: GetMagnitudeNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 615994
        vector: {x: 0, y: 0}
    - rid: 8923418986610950174
      type: {class: IfElseNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 890686
        conditionPort: 0
    - rid: 8923418986610950175
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 819546
        key: rangeX
    - rid: 8923418986610950176
      type: {class: FloatDivideNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 407106
        a: 0
        b: 0
    - rid: 8923418986610950177
      type: {class: PlayVfxInWorldPositionWithScaleNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 117754
        ownerOnly: 0
        source: {x: 0, y: 0}
        vfxName: Vfx_001_Park_00_CpAbility
        sfxName: Sfx_001_Park_00_CpAbility
        degree: 0
        scaleX: 0
        scaleY: 1
    - rid: 8923418986610950178
      type: {class: ClampNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 637916
        value: 0
        min: 5000
        max: 0
    - rid: 8923418986610950179
      type: {class: IntSubtractNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 686009
        a: 0
        b: 0
    - rid: 8923418986610950180
      type: {class: GetVector2XYNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 935746
        value: {x: 0, y: 0}
    - rid: 8923418986610950181
      type: {class: GetVector2XYNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 412247
        value: {x: 0, y: 0}
    - rid: 8923418986610950182
      type: {class: IntSubtractNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 222448
        a: 0
        b: 0
    - rid: 8923418986610950183
      type: {class: AddCameraShakeByActorNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 644379
        ownerOnly: 0
        shakeId: 30011001
    - rid: 8923418986610950184
      type: {class: AddCameraShakeByActorNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 510086
        ownerOnly: 0
        shakeId: 30011002
    - rid: 8923418986610950185
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 661463
    - rid: 8923418986610950186
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 224309
    - rid: 8923418986610950187
      type: {class: GetCharactersInRectangleNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 570515
        excludeAlly: 1
        excludeEnemy: 0
        width: 0
        height: 0
        fromRoot: 0
        offsetX: 0
        offsetY: 0
        angle: 0
    - rid: 8923418986610950188
      type: {class: CountNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 691905
    - rid: 8923418986610950189
      type: {class: GreaterNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 70093
        a: 0
        b: 0
    - rid: 8923418986610950190
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 438419
    - rid: 8923418986610950191
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 897766
        key: rangeY
    - rid: 8923418986610950192
      type: {class: GetCharacterCenterNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 994547
    - rid: 8923418986610950193
      type: {class: GetVector2XYNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 630274
        value: {x: 0, y: 0}
    - rid: 8923418986610950194
      type: {class: GetVector2XYNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 764999
        value: {x: 0, y: 0}
    - rid: 8923418986610950195
      type: {class: IntSubtractNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 239102
        a: 0
        b: 0
    - rid: 8923418986610950196
      type: {class: IntSubtractNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 963243
        a: 0
        b: 0
    - rid: 8923418986610950197
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 869753
    - rid: 8923418986610950198
      type: {class: GetVectorNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 384562
        a: {x: 0, y: 0}
        b: {x: 0, y: 0}
    - rid: 8923418986610950199
      type: {class: GetMidpointNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 728640
        a: {x: 0, y: 0}
        b: {x: 0, y: 0}
    - rid: 8923418986610950200
      type: {class: GetVector2XYNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 894970
        value: {x: 0, y: 0}
    - rid: 8923418986610950201
      type: {class: GetDirectionInDegreeNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 689763
        vector: {x: 0, y: 0}
    - rid: 8923418986610950202
      type: {class: GetMagnitudeNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 59268
        vector: {x: 0, y: 0}
    - rid: 8923418986610950203
      type: {class: StringNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 708498
        text: "\u53D6\u5F97\u5169\u9EDE\u9593\u7684\u5411\u91CF"
    - rid: 8923418986610950204
      type: {class: StringNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 928484
        text: "\u53D6\u5F97\u8A72\u4F4D\u7F6E/\u5411\u91CF\u7684(x,y)"
    - rid: 8923418986610950205
      type: {class: StringNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 570123
        text: "\u53D6\u5F97\u5169\u9EDE\u9593\u7684\u4E2D\u9593\u9EDE"
    - rid: 8923418986610950206
      type: {class: StringNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 418484
        text: "\u53D6\u5F97\u5169\u9EDE\u9593\u7684\u65B9\u5411(\u914D\u5408\u5411\u91CF\u4F7F\u7528)"
    - rid: 8923418986610950207
      type: {class: StringNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 860107
        text: "\u53D6\u5F97\u5169\u9EDE\u9593\u7684\u8DDD\u96E2"
    - rid: 8923418986610950208
      type: {class: GetCharactersInRectangleNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 621684
        excludeAlly: 1
        excludeEnemy: 0
        width: 0
        height: 0
        fromRoot: 0
        offsetX: 0
        offsetY: 0
        angle: 0
    - rid: 8923418986610950209
      type: {class: StringNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 703625
        text: "\u8A08\u7B97\u7BC4\u570D\u5167\u7684\u89D2\u8272"
    - rid: 8923418986610950210
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 211602
    - rid: 8923418986610950211
      type: {class: AppendAbilityNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 820407
        gameplayAbilityId: -1
        groupIndex: 4
        ticks: -1
        param0: 0
        param1: 0
        param2: 0
        param3: 0
        overwrite: 1
        ignoreClearWhenStunned: 0
        showDurationHUD: 0
    - rid: 8923418986610950212
      type: {class: StringNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 994730
        text: "\u7D66\u4E88\u56DE\u65AC"
    - rid: 8923418986610950213
      type: {class: StatRefreshEventNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 246882
        order: 0
    - rid: 8923418986610950214
      type: {class: LockVelocityModifierNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 203326
        x: 0
        y: 1
    - rid: 8923418986610950215
      type: {class: StatRefreshEventNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 974112
        order: 0
    - rid: 8923418986610950216
      type: {class: InvulnerableModifierNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 779560
    - rid: 8923418986610950217
      type: {class: SetAbilityCooldownNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 69427
        gameplayAbilityId: 0
        groupIndex: 0
        cooldownTicks: 0
        ignoreSetCooldownTicks: 0
    - rid: 8923418986610950218
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 210833
        key: cooldown
    - rid: 8923418986610950219
      type: {class: OnProjectileApplyStunNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 162131
    - rid: 8923418986610950220
      type: {class: AbilityLogNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 645136
        eventType: 2
        param1: 1
        param2: 0
        param3: 0
        param4: 0
    - rid: 8923418986610950221
      type: {class: OnObjectBreakOtherData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 293069
    - rid: 8923418986610950222
      type: {class: AbilityLogNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 825310
        eventType: 11
        param1: 0
        param2: 1
        param3: 0
        param4: 0
    - rid: 8923418986610950223
      type: {class: GetItemAbilityAtSlotNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 921370
        index: 0
    - rid: 8923418986610950224
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 663024
    - rid: 8923418986610950225
      type: {class: LogNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 61813
        text: 0
    - rid: 8923418986610950226
      type: {class: StringNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 10404
        text: "\u56DE\u65AC"
    - rid: 8923418986610950227
      type: {class: AddCameraBumpByActorNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 432049
        ownerOnly: 1
        bumpId: 3001100
    - rid: 8923418986610950228
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 139555
    - rid: 8923418986610950229
      type: {class: RemoveAbilityNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 214743
        gameplayAbilityId: -1
        groupIndex: 1
    - rid: 8923418986610950230
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 753455
    - rid: 8923418986610950231
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 211818
    - rid: 8923418986610950232
      type: {class: PlayVfxNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 433528
        ownerOnly: 0
        vfxName: Vfx_001_Park_00_CpAbility_Trail
        sfxName: 
        degree: 0
        boneType: 0
        customBoneId: 0
        offsetX: 0
        offsetY: 0
        isInWorld: 0
    - rid: 8923418986610950233
      type: {class: CreateAreaNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 688042
        size: 0
        size2: 0
        rotation: 0
        offsetX: 0
        offsetY: 0
        speed: 0
        faceDirection: 0
        lifetime: 0
        deactivationTime: 0
        stunTicks: 0
        energyGainForHit: 0
        canBreakProjectile: 1
        hitPeriodTick: 0
        gameplayAbilityId: -1
        groupIndex: 3
        vfxDataId: 0
        hitFxDataId: 0
        discardPrevious: 0
        triggerPreviousFinished: 0
        lockX: 0
        lockY: 0
        canHitLevelTrap: 0
        canBreakLevelTrap: 0
        ignoreHitIfPreviousHit: 0
        shapeType: 2
        pathType: 0
        rootType: 1
        pathArg1: 0
        pathArg2: 0
        pathArg3: 0
    - rid: 8923418986610950234
      type: {class: CueCVNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 880502
        abilityCVId: 3001100
    - rid: 8923418986610950235
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 680401
    - rid: 8923418986610950236
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 229372
        key: invulnerable
    - rid: 8923418986610950237
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 861585
    - rid: 8923418986610950238
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 947673
        key: stun
    - rid: 8923418986610950239
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 114151
        key: energyRecovery
    - rid: 8923418986610950240
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 142363
        key: rangeY
    - rid: 8923418986610950241
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 303200
        key: lifetime
    - rid: 8923418986610950242
      type: {class: AppendAbilityNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 684069
        gameplayAbilityId: -1
        groupIndex: 2
        ticks: 0
        param0: 0
        param1: 0
        param2: 0
        param3: 0
        overwrite: 1
        ignoreClearWhenStunned: 0
        showDurationHUD: 0
    - rid: 8923418986610950243
      type: {class: TeleportNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 468498
        offsetX: 0
        offsetY: 0
        ignorePickup: 0
    - rid: 8923418986610950244
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 103789
        key: rangeX0
    - rid: 8923418986610950245
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 881662
    - rid: 8923418986610950246
      type: {class: GetMidpointNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 433755
        a: {x: 0, y: 0}
        b: {x: 0, y: 0}
    - rid: 8923418986610950247
      type: {class: PlayVfxInWorldPositionNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 223808
        ownerOnly: 0
        source: {x: 0, y: 0}
        vfxName: Vfx_001_Park_00_CpAbility_CharHit
        sfxName: 
        degree: 0
    - rid: 8923418986610950248
      type: {class: GetVectorNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 382587
        a: {x: 0, y: 0}
        b: {x: 0, y: 0}
    - rid: 8923418986610950249
      type: {class: GetDirectionInDegreeNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 125611
        vector: {x: 0, y: 0}
    - rid: 8923418986610950250
      type: {class: FloatDivideNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 267142
        a: 0
        b: 10000
    - rid: 8923418986610950251
      type: {class: GetMagnitudeNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 114418
        vector: {x: 0, y: 0}
    - rid: 8923418986610950252
      type: {class: IfElseNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 681026
        conditionPort: 0
    - rid: 8923418986610950253
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 635257
        key: rangeX0
    - rid: 8923418986610950254
      type: {class: FloatDivideNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 290804
        a: 0
        b: 0
    - rid: 8923418986610950255
      type: {class: PlayVfxInWorldPositionWithScaleNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 173347
        ownerOnly: 0
        source: {x: 0, y: 0}
        vfxName: Vfx_001_Park_00_CpAbility
        sfxName: Sfx_001_Park_00_CpAbility
        degree: 0
        scaleX: 0
        scaleY: 1
    - rid: 8923418986610950256
      type: {class: ClampNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 765106
        value: 0
        min: 5000
        max: 0
    - rid: 8923418986610950257
      type: {class: IntSubtractNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 306494
        a: 0
        b: 0
    - rid: 8923418986610950258
      type: {class: GetVector2XYNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 596821
        value: {x: 0, y: 0}
    - rid: 8923418986610950259
      type: {class: GetVector2XYNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 661649
        value: {x: 0, y: 0}
    - rid: 8923418986610950260
      type: {class: IntSubtractNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 458012
        a: 0
        b: 0
    - rid: 8923418986610950261
      type: {class: AddCameraShakeByActorNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 858876
        ownerOnly: 0
        shakeId: 30011001
    - rid: 8923418986610950262
      type: {class: AddCameraShakeByActorNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 494892
        ownerOnly: 0
        shakeId: 30011002
    - rid: 8923418986610950263
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 264166
    - rid: 8923418986610950264
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 493074
    - rid: 8923418986610950265
      type: {class: GetCharactersInRectangleNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 617363
        excludeAlly: 1
        excludeEnemy: 0
        width: 0
        height: 0
        fromRoot: 0
        offsetX: 0
        offsetY: 0
        angle: 0
    - rid: 8923418986610950266
      type: {class: CountNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 819354
    - rid: 8923418986610950267
      type: {class: GreaterNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 312039
        a: 0
        b: 0
    - rid: 8923418986610950268
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 726775
    - rid: 8923418986610950269
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 834685
        key: rangeY
    - rid: 8923418986610950270
      type: {class: GetCharacterCenterNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 237163
    - rid: 8923418986610950271
      type: {class: GetVector2XYNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 598809
        value: {x: 0, y: 0}
    - rid: 8923418986610950272
      type: {class: GetVector2XYNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 933724
        value: {x: 0, y: 0}
    - rid: 8923418986610950273
      type: {class: IntSubtractNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 912583
        a: 0
        b: 0
    - rid: 8923418986610950274
      type: {class: IntSubtractNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 162039
        a: 0
        b: 0
    - rid: 8923418986610950275
      type: {class: SelfNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 941799
    - rid: 8923418986610950276
      type: {class: OnActivatingData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 439693
        ignoreStun: 0
    - rid: 8923418986610950277
      type: {class: AppendAbilityNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 771563
        gameplayAbilityId: -1
        groupIndex: 1
        ticks: 0
        param0: 0
        param1: 0
        param2: 0
        param3: 0
        overwrite: 0
        ignoreClearWhenStunned: 0
        showDurationHUD: 0
    - rid: 8923418986610950278
      type: {class: OnTickNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 36262
        ignoreStun: 0
        ignoreKnockedDown: 0
    - rid: 8923418986610950279
      type: {class: DelayNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 179257
        ticks: 0
        nextActionTickSlot: 0
        countSlot: 1
    - rid: 8923418986610950280
      type: {class: BlackboardNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 977079
        key: delay
    - rid: 8923418986610950281
      type: {class: RemoveAbilityNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 531595
        gameplayAbilityId: -1
        groupIndex: 4
    - rid: 8923418986610950282
      type: {class: LogNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 69725
        text: 
    - rid: 8923418986610950283
      type: {class: GetAbilityByGroupNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 858153
        group: 0
    - rid: 8923418986610950284
      type: {class: StringNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 973417
        text: "\u6C92\u6709\u7B49\u7D1A\u7684ID(\u627E\u7B2C\u4E00\u500B)"
    - rid: 8923418986610950285
      type: {class: GetAbilityCooldownNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 441190
        gameplayAbilityId: 0
        groupIndex: 0
    - rid: 8923418986610950286
      type: {class: MaxNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 171083
        a: 0
        b: 0
    - rid: 8923418986610950287
      type: {class: SetAbilityCooldownNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 766002
        gameplayAbilityId: 0
        groupIndex: 0
        cooldownTicks: 0
        ignoreSetCooldownTicks: 1
    - rid: 8923418986610950288
      type: {class: EnergyCostMultiplierOffsetModifierNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 904294
        modifier: 0
    - rid: 8923418986610950289
      type: {class: StringNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 796870
        text: "\u8A2D\u5B9A\u51B7\u537B"
    - rid: 8923418986610950290
      type: {class: StringNodeData, ns: Fanimax.CP.Gameplay.AbilityServices, asm: Fanimax.CP.Gameplay.AbilityServices}
      data:
        id: 287759
        text: "\u6E1B\u5C11\u6D88\u8017"
